import { FiGithub, FiLinkedin } from 'react-icons/fi';
import styled from 'styled-components';

const FooterContainer = styled.footer`
  background-color: var(--primary);
  padding: 2rem 0;
  border-top: 1px solid rgba(136, 146, 176, 0.1);
`;

const FooterInner = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;

  @media (min-width: 640px) {
    padding: 0 1.5rem;
  }

  @media (min-width: 1024px) {
    padding: 0 2rem;
  }
`;

const FooterContent = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;

  @media (min-width: 768px) {
    flex-direction: row;
  }
`;

const FooterText = styled.p`
  color: var(--tertiary);
  font-size: 0.875rem;
  margin-bottom: 1rem;

  @media (min-width: 768px) {
    margin-bottom: 0;
  }
`;

const SocialLinks = styled.div`
  display: flex;
  gap: 1.5rem;
`;

const SocialLink = styled.a`
  color: var(--tertiary);
  transition: color 0.3s ease;

  &:hover {
    color: var(--secondary);
  }

  svg {
    width: 1.25rem;
    height: 1.25rem;
  }
`;

const Footer = () => {
  return (
    <FooterContainer>
      <FooterInner>
        <FooterContent>
          <FooterText>
            &copy; {new Date().getFullYear()} Michael Hoang. All rights reserved.
          </FooterText>

          <SocialLinks>
            <SocialLink
              href="https://github.com/MikeDominic92"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="GitHub"
            >
              <FiGithub />
            </SocialLink>
            <SocialLink
              href="https://www.linkedin.com/in/mdhlee/"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="LinkedIn"
            >
              <FiLinkedin />
            </SocialLink>

          </SocialLinks>
        </FooterContent>
      </FooterInner>
    </FooterContainer>
  );
};

export default Footer;
