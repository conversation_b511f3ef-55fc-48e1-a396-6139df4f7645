import { useEffect } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { FiTrash2, FiAlertCircle } from 'react-icons/fi';
import { unsaveJob } from '../../services/jobStorage';
import { useJobContext } from '../../context/JobContext';
import JobCard from './JobCard';

const SavedJobs: React.FC = () => {
  const { savedJobs, refreshJobs } = useJobContext();

  useEffect(() => {
    // Load saved jobs when component mounts
    refreshJobs();
  }, []);

  const handleClearAll = () => {
    // Show confirmation dialog
    if (window.confirm('Are you sure you want to remove all saved jobs?')) {
      // Remove all saved jobs
      savedJobs.forEach(job => unsaveJob(job.job_id));
      refreshJobs();
    }
  };

  return (
    <SavedJobsContainer>
      <SavedJobsHeader>
        <h2>Saved Jobs</h2>
        {savedJobs.length > 0 && (
          <ClearAllButton onClick={handleClearAll}>
            <FiTrash2 />
            Clear All
          </ClearAllButton>
        )}
      </SavedJobsHeader>

      {savedJobs.length === 0 ? (
        <NoSavedJobs>
          <FiAlertCircle />
          <p>You haven't saved any jobs yet.</p>
          <p>When you find a job you're interested in, click the bookmark icon to save it for later.</p>
        </NoSavedJobs>
      ) : (
        <SavedJobsList>
          <AnimatePresence>
            {savedJobs.map(job => (
              <motion.div
                key={job.job_id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
              >
                <JobCard job={job} />
              </motion.div>
            ))}
          </AnimatePresence>
        </SavedJobsList>
      )}
    </SavedJobsContainer>
  );
};

const SavedJobsContainer = styled.div`
  width: 100%;
  padding: 1.5rem;
`;

const SavedJobsHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;

  h2 {
    color: var(--light);
    margin: 0;
  }
`;

const ClearAllButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid var(--tertiary);
  border-radius: 4px;
  background-color: transparent;
  color: var(--tertiary);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: tomato;
    color: tomato;
  }
`;

const NoSavedJobs = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
  color: var(--tertiary);
  text-align: center;

  svg {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  p {
    margin: 0.5rem 0;
    max-width: 400px;
  }
`;

const SavedJobsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

export default SavedJobs;
