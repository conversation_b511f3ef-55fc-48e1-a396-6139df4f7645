import React, { useState } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { FiSearch, FiBookmark } from 'react-icons/fi';
import { JobProvider } from '../../context/JobContext';
import SearchFilters from './SearchFilters';
import JobList from './JobList';
import SavedJobs from './SavedJobs';

const JobSearchApp: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'search' | 'saved'>('search');

  return (
    <JobProvider>
      <JobSearchContainer>
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <JobSearchHeader>
            <h1>Job Board Aggregator</h1>
            <p>Search across multiple job boards in one place</p>
          </JobSearchHeader>
        </motion.div>

        <TabsContainer>
          <Tab
            active={activeTab === 'search'}
            onClick={() => setActiveTab('search')}
          >
            <FiSearch />
            <span>Search Jobs</span>
          </Tab>
          <Tab
            active={activeTab === 'saved'}
            onClick={() => setActiveTab('saved')}
          >
            <FiBookmark />
            <span>Saved Jobs</span>
          </Tab>
        </TabsContainer>

        {activeTab === 'search' ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            <SearchFilters />
            <JobList />
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            <SavedJobs />
          </motion.div>
        )}
      </JobSearchContainer>
    </JobProvider>
  );
};

const JobSearchContainer = styled.div`
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
`;

const JobSearchHeader = styled.div`
  text-align: center;
  margin-bottom: 2rem;

  h1 {
    color: var(--light);
    margin-bottom: 0.5rem;
  }

  p {
    color: var(--tertiary);
    font-size: 1.1rem;
  }
`;

const TabsContainer = styled.div`
  display: flex;
  margin-bottom: 2rem;
  border-bottom: 1px solid rgba(137, 146, 176, 0.2);
`;

interface TabProps {
  active: boolean;
}

const Tab = styled.button<TabProps>`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  border-bottom: 2px solid ${props => (props.active ? 'var(--secondary)' : 'transparent')};
  color: ${props => (props.active ? 'var(--secondary)' : 'var(--tertiary)')};
  font-size: 1rem;
  font-weight: ${props => (props.active ? '500' : '400')};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    color: ${props => (props.active ? 'var(--secondary)' : 'var(--light)')};
  }
`;

export default JobSearchApp;
