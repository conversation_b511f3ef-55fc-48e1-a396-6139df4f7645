{"name": "portfolio", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@types/styled-components": "^5.1.34", "framer-motion": "^12.6.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-scroll": "^1.9.3", "styled-components": "^6.1.17"}, "devDependencies": {"@eslint/js": "^9.21.0", "@tailwindcss/postcss": "^4.1.2", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-scroll": "^1.8.10", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.2", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}