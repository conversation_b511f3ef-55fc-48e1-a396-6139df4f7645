# ACAP Website Deployment Instructions

## Option 1: Netlify Drag & Drop (Recommended)

1. Go to [netlify.com](https://netlify.com)
2. Sign up or log in to your account
3. On your dashboard, look for the "Sites" section
4. Drag and drop the entire `ACAP-Redesign` folder onto the deployment area
5. <PERSON><PERSON> will automatically deploy your site and provide you with a URL

## Option 2: GitHub + Netlify (For ongoing updates)

1. Create a new repository on GitHub
2. Upload all files from the `ACAP-Redesign` folder to the repository
3. Go to [netlify.com](https://netlify.com) and click "New site from Git"
4. Connect your GitHub account and select the repository
5. Set the publish directory to `/` (root)
6. Click "Deploy site"

## Option 3: Netlify CLI (Advanced)

If you have Netlify CLI installed:

```bash
cd ACAP-Redesign
netlify init
netlify deploy --prod
```

## Custom Domain Setup

Once deployed, you can:
1. Go to your site settings in Netlify
2. Click "Domain management"
3. Add a custom domain (e.g., acaponline.org)
4. Follow <PERSON><PERSON>'s instructions to update your DNS settings

## Site Features

Your deployed site includes:
- ✅ Responsive design for all devices
- ✅ Professional medical organization layout
- ✅ Multiple pages (About, Events, Programs, Support Us)
- ✅ Working navigation between pages
- ✅ Contact information and social media links
- ✅ Modern CSS styling with hover effects
- ✅ Font Awesome icons
- ✅ Google Fonts integration

## Files Included

- `index.html` - Homepage (About page)
- `events.html` - Events listing page
- `programs.html` - Programs and services page
- `donate.html` - Support/donation page
- `css/styles.css` - All styling
- `js/main.js` - JavaScript functionality
- `netlify.toml` - Netlify configuration

The site is ready for immediate deployment!
