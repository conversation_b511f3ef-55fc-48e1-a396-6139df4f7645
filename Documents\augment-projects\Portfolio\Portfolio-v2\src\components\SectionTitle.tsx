import { motion } from 'framer-motion';
import styled from 'styled-components';

interface SectionTitleProps {
  number: string;
  title: string;
}

const TitleContainer = styled(motion.div)`
  display: flex;
  align-items: center;
  margin-bottom: 3rem;
  width: 100%;
  justify-content: center;
  text-align: center;
  position: relative;
`;

const SectionNumber = styled.span`
  color: var(--secondary);
  font-family: 'Roboto Mono', monospace;
  margin-right: 0.5rem;
`;

const Title = styled.h2`
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--light);

  @media (min-width: 768px) {
    font-size: 2.25rem;
  }
`;

const Divider = styled.div`
  width: 30%;
  height: 1px;
  background-color: rgba(136, 146, 176, 0.3);
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);

  @media (max-width: 768px) {
    width: 20%;
  }
`;

const LeftDivider = styled(Divider)`
  right: auto;
  left: 0;
`;

const TitleWrapper = styled.div`
  display: flex;
  align-items: center;
  z-index: 1;
  padding: 0 20px;
  background-color: inherit;
`;

const SectionTitle = ({ number, title }: SectionTitleProps) => {
  return (
    <TitleContainer
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: "-100px" }}
      transition={{ duration: 0.5 }}
    >
      <LeftDivider />
      <TitleWrapper>
        <SectionNumber>{number}.</SectionNumber>
        <Title>{title}</Title>
      </TitleWrapper>
      <Divider />
    </TitleContainer>
  );
};

export default SectionTitle;
