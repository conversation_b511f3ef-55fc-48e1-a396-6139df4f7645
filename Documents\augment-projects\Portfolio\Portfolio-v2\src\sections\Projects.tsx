import { useState } from 'react';
import { FiGithub, FiExternalLink } from 'react-icons/fi';
import SectionTitle from '../components/SectionTitle';
import FadeInSection from '../components/FadeInSection';

type Project = {
  title: string;
  description: string;
  tags: string[];
  github?: string;
  external?: string;
  image: string;
};

const Projects = () => {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  const projects: Project[] = [
    {
      title: 'Predictive Threat Intelligence Platform',
      description: 'Developed a comprehensive platform that leverages AI to predict and identify potential cloud vulnerabilities before they can be exploited. The system analyzes patterns from historical security incidents to proactively strengthen cloud infrastructure security posture.',
      tags: ['Python', 'AI/ML', 'Cloud Security', 'Threat Intelligence'],
      github: 'https://github.com/MikeDominic92/Predictive-Threat-Intelligence-Platform-for-Cloud-Vulnerabilities',
      image: '/projects/threat-intelligence.jpg'
    },
    {
      title: 'Cloud Security AI Monitor for Google',
      description: 'Created an AI-powered monitoring solution specifically for Google Cloud Platform that continuously scans for security anomalies, misconfigurations, and potential vulnerabilities. Features real-time alerts and automated remediation suggestions.',
      tags: ['Python', 'Google Cloud', 'AI/ML', 'Security Monitoring'],
      github: 'https://github.com/MikeDominic92/cloud-security-ai-monitor-Google',
      image: '/projects/cloud-security.jpg'
    },
    {
      title: 'Advanced Ticket System',
      description: 'Built a modern ticket management system with automated prioritization, intelligent routing, and integration with multiple communication channels. Designed to streamline support workflows and improve response times.',
      tags: ['React', 'Node.js', 'MongoDB', 'Automation'],
      github: 'https://github.com/MikeDominic92/Advanced-Ticket-System',
      image: '/projects/ticket-system.jpg'
    }
  ];

  return (
    <section id="projects" className="py-20 bg-primary">
      <div className="section-container">
        <SectionTitle number="04" title="Projects" />

        <div className="grid grid-cols-1 gap-8 mt-10 w-full">
          {projects.map((project, index) => (
            <FadeInSection key={index} delay={index * 0.1} direction={index % 2 === 0 ? 'right' : 'left'}>
              <div
                className="relative overflow-hidden rounded-lg bg-dark border border-tertiary/20 hover:border-secondary/50 transition-all duration-300"
                onMouseEnter={() => setHoveredIndex(index)}
                onMouseLeave={() => setHoveredIndex(null)}
              >
                <div className="grid md:grid-cols-2 gap-6 p-6">
                  <div className="order-2 md:order-1">
                    <h3 className="text-xl font-bold text-light mb-3">{project.title}</h3>
                    <p className="text-tertiary mb-4">{project.description}</p>

                    <div className="flex flex-wrap gap-2 mb-6">
                      {project.tags.map((tag, tagIndex) => (
                        <span
                          key={tagIndex}
                          className="text-xs font-mono px-2 py-1 rounded bg-secondary/10 text-secondary"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>

                    <div className="flex gap-4">
                      {project.github && (
                        <a
                          href={project.github}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-tertiary hover:text-secondary transition-colors"
                          aria-label={`GitHub repository for ${project.title}`}
                        >
                          <FiGithub size={20} />
                        </a>
                      )}
                      {project.external && (
                        <a
                          href={project.external}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-tertiary hover:text-secondary transition-colors"
                          aria-label={`Live demo for ${project.title}`}
                        >
                          <FiExternalLink size={20} />
                        </a>
                      )}
                    </div>
                  </div>

                  <div className="order-1 md:order-2 relative">
                    <div className="relative overflow-hidden rounded-md bg-secondary/5 aspect-video">
                      <div
                        className="absolute inset-0 bg-cover bg-center transition-transform duration-500 ease-in-out"
                        style={{
                          backgroundImage: `url(${project.image})`,
                          transform: hoveredIndex === index ? 'scale(1.05)' : 'scale(1)',
                          backgroundColor: 'rgba(100, 255, 218, 0.05)'
                        }}
                      >
                        {/* Fallback content if image doesn't load */}
                        <div className="absolute inset-0 flex items-center justify-center text-secondary font-mono text-sm">
                          {project.title}
                        </div>
                      </div>
                      <div className="absolute inset-0 bg-primary/50 hover:bg-primary/30 transition-colors duration-300" />
                    </div>
                  </div>
                </div>
              </div>
            </FadeInSection>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Projects;
