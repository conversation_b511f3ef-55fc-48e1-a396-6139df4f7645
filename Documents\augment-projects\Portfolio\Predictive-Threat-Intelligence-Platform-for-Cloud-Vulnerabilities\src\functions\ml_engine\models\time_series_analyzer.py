"""
Time Series Analyzer for Threat Intelligence Data

This module applies time series analysis techniques to identify patterns and trends
in threat intelligence data over time.
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from sklearn.linear_model import LinearRegression

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze(features):
    """
    Analyze threat data using time series techniques to identify patterns and trends.
    
    Args:
        features (dict): Features extracted from threat data
        
    Returns:
        dict: Time series analysis results including trends and forecasts
    """
    try:
        logger.info("Starting time series analysis of threat data")
        
        # Extract temporal features
        temporal_data = extract_temporal_features(features)
        
        # If insufficient temporal data, return early
        if not temporal_data or len(temporal_data) < 2:
            logger.warning("Insufficient temporal data for time series analysis")
            return {
                'trend': 'unknown',
                'forecast': None,
                'seasonality': None,
                'insufficient_data': True
            }
        
        # Analyze trend
        trend_analysis = analyze_trend(temporal_data)
        
        # Detect seasonality
        seasonality = detect_seasonality(temporal_data)
        
        # Generate forecast
        forecast = generate_forecast(temporal_data, trend_analysis)
        
        # Combine results
        results = {
            'trend': trend_analysis['trend'],
            'trend_confidence': trend_analysis['confidence'],
            'forecast': forecast,
            'seasonality': seasonality,
            'data_points_analyzed': len(temporal_data)
        }
        
        logger.info(f"Time series analysis complete. Trend: {results['trend']}, Confidence: {results['trend_confidence']:.2f}")
        return results
    
    except Exception as e:
        logger.error(f"Error in time series analysis: {e}", exc_info=True)
        return {
            'trend': 'unknown',
            'forecast': None,
            'seasonality': None,
            'error': str(e)
        }

def extract_temporal_features(features):
    """
    Extract temporal features from the threat data.
    
    Args:
        features (dict): Features extracted from threat data
        
    Returns:
        list: List of dictionaries with temporal features
    """
    temporal_data = []
    
    # Try to extract timestamps from the features
    timestamps = []
    
    # Check for created/modified timestamps
    if 'created' in features:
        try:
            timestamps.append({
                'timestamp': parse_timestamp(features['created']),
                'type': 'created'
            })
        except:
            pass
    
    if 'modified' in features:
        try:
            timestamps.append({
                'timestamp': parse_timestamp(features['modified']),
                'type': 'modified'
            })
        except:
            pass
    
    # Check for pulses or reports with timestamps
    if 'pulses' in features and isinstance(features['pulses'], list):
        for pulse in features['pulses']:
            if isinstance(pulse, dict) and 'created' in pulse:
                try:
                    timestamps.append({
                        'timestamp': parse_timestamp(pulse['created']),
                        'type': 'pulse_created',
                        'data': pulse
                    })
                except:
                    pass
    
    # Check for indicators with timestamps
    if 'indicators' in features and isinstance(features['indicators'], list):
        for indicator in features['indicators']:
            if isinstance(indicator, dict) and 'created' in indicator:
                try:
                    timestamps.append({
                        'timestamp': parse_timestamp(indicator['created']),
                        'type': 'indicator_created',
                        'data': indicator
                    })
                except:
                    pass
    
    # Sort timestamps chronologically
    timestamps.sort(key=lambda x: x['timestamp'])
    
    return timestamps

def parse_timestamp(timestamp_str):
    """
    Parse a timestamp string into a datetime object.
    
    Args:
        timestamp_str (str): Timestamp string
        
    Returns:
        datetime: Parsed datetime object
    """
    # Try different timestamp formats
    formats = [
        '%Y-%m-%dT%H:%M:%S.%fZ',  # ISO format with microseconds
        '%Y-%m-%dT%H:%M:%SZ',     # ISO format without microseconds
        '%Y-%m-%d %H:%M:%S',      # Standard format
        '%Y-%m-%d'                # Date only
    ]
    
    for fmt in formats:
        try:
            return datetime.strptime(timestamp_str, fmt)
        except ValueError:
            continue
    
    # If all formats fail, raise an exception
    raise ValueError(f"Unable to parse timestamp: {timestamp_str}")

def analyze_trend(temporal_data):
    """
    Analyze the trend in temporal data.
    
    Args:
        temporal_data (list): List of dictionaries with temporal features
        
    Returns:
        dict: Trend analysis results
    """
    # Extract timestamps as numerical values (days since first timestamp)
    if not temporal_data:
        return {'trend': 'unknown', 'confidence': 0.0}
    
    first_timestamp = temporal_data[0]['timestamp']
    x = np.array([(entry['timestamp'] - first_timestamp).total_seconds() / 86400 for entry in temporal_data])
    
    # Create a simple count-based y value (cumulative count over time)
    y = np.array(range(1, len(temporal_data) + 1))
    
    # Reshape for sklearn
    x = x.reshape(-1, 1)
    
    # Fit linear regression to identify trend
    model = LinearRegression()
    model.fit(x, y)
    
    # Get slope and R² value
    slope = model.coef_[0]
    r_squared = model.score(x, y)
    
    # Determine trend based on slope
    if slope > 0.1:  # More than 0.1 events per day
        trend = 'increasing'
    elif slope < -0.1:  # Fewer than -0.1 events per day
        trend = 'decreasing'
    else:
        trend = 'stable'
    
    return {
        'trend': trend,
        'slope': float(slope),
        'confidence': float(r_squared)
    }

def detect_seasonality(temporal_data):
    """
    Detect seasonality patterns in temporal data.
    
    Args:
        temporal_data (list): List of dictionaries with temporal features
        
    Returns:
        dict: Seasonality detection results
    """
    # Need sufficient data points for seasonality detection
    if len(temporal_data) < 10:
        return {
            'detected': False,
            'reason': 'insufficient_data'
        }
    
    # Extract timestamps and convert to pandas Series
    timestamps = [entry['timestamp'] for entry in temporal_data]
    dates = pd.Series(timestamps)
    
    # Count events by day of week
    day_counts = dates.dt.dayofweek.value_counts().sort_index()
    day_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    day_of_week = {day_names[i]: int(count) for i, count in enumerate(day_counts) if i in day_counts}
    
    # Count events by hour of day
    hour_counts = dates.dt.hour.value_counts().sort_index()
    hour_of_day = {str(i): int(count) for i, count in enumerate(hour_counts) if i in hour_counts}
    
    # Simple detection: check if some days/hours have significantly more events
    day_std = np.std(list(day_of_week.values())) if day_of_week else 0
    day_mean = np.mean(list(day_of_week.values())) if day_of_week else 0
    day_cv = day_std / day_mean if day_mean > 0 else 0
    
    hour_std = np.std(list(hour_of_day.values())) if hour_of_day else 0
    hour_mean = np.mean(list(hour_of_day.values())) if hour_of_day else 0
    hour_cv = hour_std / hour_mean if hour_mean > 0 else 0
    
    # Determine if seasonality exists based on coefficient of variation
    daily_seasonality = hour_cv > 0.5
    weekly_seasonality = day_cv > 0.5
    
    return {
        'detected': daily_seasonality or weekly_seasonality,
        'daily_seasonality': daily_seasonality,
        'weekly_seasonality': weekly_seasonality,
        'day_of_week_distribution': day_of_week,
        'hour_of_day_distribution': hour_of_day
    }

def generate_forecast(temporal_data, trend_analysis):
    """
    Generate a forecast based on temporal data and trend analysis.
    
    Args:
        temporal_data (list): List of dictionaries with temporal features
        trend_analysis (dict): Results of trend analysis
        
    Returns:
        dict: Forecast results
    """
    # Need sufficient data and a clear trend for forecasting
    if len(temporal_data) < 5 or trend_analysis['confidence'] < 0.5:
        return {
            'generated': False,
            'reason': 'insufficient_data_or_confidence'
        }
    
    # Extract timestamps
    timestamps = [entry['timestamp'] for entry in temporal_data]
    
    # Create features for forecasting
    first_timestamp = timestamps[0]
    x = np.array([(ts - first_timestamp).total_seconds() / 86400 for ts in timestamps])
    y = np.array(range(1, len(timestamps) + 1))
    
    # Reshape for sklearn
    x = x.reshape(-1, 1)
    
    # Fit linear regression model
    model = LinearRegression()
    model.fit(x, y)
    
    # Generate forecast for next 7, 14, and 30 days
    last_timestamp = timestamps[-1]
    forecast_days = [7, 14, 30]
    forecast_points = []
    
    for days in forecast_days:
        future_day = (last_timestamp + timedelta(days=days) - first_timestamp).total_seconds() / 86400
        predicted_count = model.predict(np.array([[future_day]]))[0]
        
        forecast_points.append({
            'days_ahead': days,
            'date': (last_timestamp + timedelta(days=days)).isoformat(),
            'predicted_cumulative_count': float(predicted_count),
            'predicted_new_events': float(predicted_count - y[-1])
        })
    
    return {
        'generated': True,
        'method': 'linear_regression',
        'confidence': float(trend_analysis['confidence']),
        'forecast_points': forecast_points
    }
