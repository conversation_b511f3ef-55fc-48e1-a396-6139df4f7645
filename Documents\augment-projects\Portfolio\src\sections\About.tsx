import SectionTitle from '../components/SectionTitle';
import FadeInSection from '../components/FadeInSection';
import TechBadges from '../components/TechBadges';

const About = () => {
  return (
    <section id="about" className="py-20 bg-primary">
      <div className="section-container">
        <SectionTitle number="01" title="About Me" />

        <div className="flex flex-col items-center w-full">
          <FadeInSection delay={0.4}>
            <div className="flex flex-col items-center mb-12">
              {/* TechBadges moved above profile picture */}
              <TechBadges className="w-full max-w-md mx-auto mb-8" />

              {/* Profile picture in bubble style */}
              <div className="relative group max-w-[120px] mb-4 cursor-pointer">
                <div className="relative z-10 bg-primary border-3 border-secondary rounded-full overflow-hidden shadow-lg">
                  <img
                    src="/profile-image.jpg"
                    alt="Michael Hoang"
                    className="w-full h-auto grayscale hover:grayscale-0 transition-all duration-300"
                  />
                  <div className="absolute inset-0 bg-primary/80 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                    <p className="text-secondary text-xs font-medium">Ain't He A Beauty</p>
                  </div>
                </div>
                <div className="absolute -inset-2 border-2 border-secondary/30 rounded-full -z-10 group-hover:translate-x-1 group-hover:translate-y-1 transition-all duration-300"></div>
              </div>

              <div className="mt-4 text-center text-secondary text-sm font-mono">
                <span className="typing-cursor">console.log('you should hire me');</span>
              </div>
            </div>
          </FadeInSection>

          <FadeInSection delay={0.2}>
            <div className="space-y-4 text-tertiary max-w-2xl mx-auto">
              <p>
                My passion for technology began early. I started by disassembling computers as a kid, and now I architect sophisticated cloud solutions on AWS and GCP. I believe in being part of innovation, not just following it. I consistently adopt emerging technologies before they become mainstream, giving me a competitive edge that translates directly to value for my employers.
              </p>

              <p>
                My professional journey began at Penn State, where I transformed a standard support role by developing automation tools that were adopted across the department. This experience shaped my leadership philosophy: solve real problems and elevate those around you. At AlgOPro Solutions, I lead mission-critical infrastructure where performance is measured in milliseconds. My team relies on me because I deliver technical excellence while investing in their professional growth. Mentoring junior engineers has become one of my most valued contributions.
              </p>

              <p>
                What distinguishes me is my ability to balance technical expertise with genuine human connection. I can immerse myself completely in complex coding challenges, but I'm equally invested in building strong relationships. My family provides valuable perspective on why we create technology in the first place. This balanced approach enables me to develop solutions that are technically robust and genuinely impactful for users.
              </p>

              <p>
                I thrive on continuous learning and seek growth beyond comfort zones. Whether contributing to open source projects, developing my team's capabilities, or organizing family adventures (occasionally without technology), I bring consistent energy and authentic leadership. Outside of technology, I explore culinary discoveries with my family, plan our next travel experience, or disconnect in nature. These diverse experiences enhance my problem-solving capabilities and creative thinking in technical contexts.
              </p>
            </div>
          </FadeInSection>
        </div>
      </div>
    </section>
  );
};

export default About;
