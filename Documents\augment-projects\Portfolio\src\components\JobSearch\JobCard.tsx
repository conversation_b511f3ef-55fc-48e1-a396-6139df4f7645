import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { FiBookmark, FiExternalLink, FiMapPin, FiClock, FiDollarSign, FiBriefcase } from 'react-icons/fi';
import { Job } from '../../types/Job';
import { saveJob, unsaveJob } from '../../services/jobStorage';
import { useJobContext } from '../../context/JobContext';

interface JobCardProps {
  job: Job;
}

const JobCard: React.FC<JobCardProps> = ({ job }) => {
  const { refreshJobs, setSelectedJob } = useJobContext();

  const handleSaveToggle = () => {
    if (job.saved) {
      unsaveJob(job.job_id);
    } else {
      saveJob(job);
    }
    refreshJobs();
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return 'Today';
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return `${diffDays} days ago`;
    } else if (diffDays < 30) {
      return `${Math.floor(diffDays / 7)} weeks ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <CardContainer
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{ scale: 1.02 }}
    >
      <CardHeader>
        <CompanyLogo>
          {job.employer_logo ? (
            <img src={job.employer_logo} alt={`${job.employer_name} logo`} />
          ) : (
            <div className="placeholder">{job.employer_name.charAt(0)}</div>
          )}
        </CompanyLogo>
        <HeaderInfo>
          <JobTitle onClick={() => setSelectedJob(job)}>{job.job_title}</JobTitle>
          <CompanyName>{job.employer_name}</CompanyName>
        </HeaderInfo>
        <SaveButton onClick={handleSaveToggle} saved={job.saved}>
          <FiBookmark />
        </SaveButton>
      </CardHeader>

      <CardBody>
        <JobDetails>
          {job.job_employment_type && (
            <DetailItem>
              <FiBriefcase />
              <span>{job.job_employment_type.replace('_', ' ')}</span>
            </DetailItem>
          )}
          {(job.job_city || job.job_country) && (
            <DetailItem>
              <FiMapPin />
              <span>
                {job.job_city ? `${job.job_city}, ` : ''}
                {job.job_state ? `${job.job_state}, ` : ''}
                {job.job_country}
                {job.job_is_remote && ' (Remote)'}
              </span>
            </DetailItem>
          )}
          {job.job_salary && (
            <DetailItem>
              <FiDollarSign />
              <span>{job.job_salary}</span>
            </DetailItem>
          )}
          <DetailItem>
            <FiClock />
            <span>{formatDate(job.job_posted_at_datetime_utc)}</span>
          </DetailItem>
        </JobDetails>

        <Description>
          {job.job_description.length > 200
            ? `${job.job_description.substring(0, 200)}...`
            : job.job_description}
        </Description>
      </CardBody>

      <CardFooter>
        <SourceTag>{job.source}</SourceTag>
        <ApplyButton href={job.job_apply_link} target="_blank" rel="noopener noreferrer">
          Apply <FiExternalLink />
        </ApplyButton>
      </CardFooter>
    </CardContainer>
  );
};

const CardContainer = styled(motion.div)`
  background-color: var(--primary);
  border: 1px solid var(--tertiary);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    border-color: var(--secondary);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  }
`;

const CardHeader = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
`;

const CompanyLogo = styled.div`
  width: 50px;
  height: 50px;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 1rem;
  background-color: var(--tertiary);
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .placeholder {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--light);
  }
`;

const HeaderInfo = styled.div`
  flex: 1;
`;

const JobTitle = styled.h3`
  margin: 0;
  font-size: 1.2rem;
  color: var(--light);
  cursor: pointer;
  
  &:hover {
    color: var(--secondary);
  }
`;

const CompanyName = styled.p`
  margin: 0.25rem 0 0;
  font-size: 0.9rem;
  color: var(--tertiary);
`;

interface SaveButtonProps {
  saved?: boolean;
}

const SaveButton = styled.button<SaveButtonProps>`
  background: none;
  border: none;
  color: ${props => (props.saved ? 'var(--secondary)' : 'var(--tertiary)')};
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    color: var(--secondary);
    transform: scale(1.1);
  }
`;

const CardBody = styled.div`
  margin-bottom: 1rem;
`;

const JobDetails = styled.div`
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 1rem;
  gap: 0.75rem;
`;

const DetailItem = styled.div`
  display: flex;
  align-items: center;
  font-size: 0.85rem;
  color: var(--tertiary);
  margin-right: 1rem;

  svg {
    margin-right: 0.25rem;
  }
`;

const Description = styled.p`
  font-size: 0.9rem;
  color: var(--light);
  line-height: 1.5;
  margin: 0;
`;

const CardFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(137, 146, 176, 0.2);
`;

const SourceTag = styled.span`
  font-size: 0.8rem;
  color: var(--tertiary);
  background-color: rgba(100, 255, 218, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
`;

const ApplyButton = styled.a`
  display: flex;
  align-items: center;
  background-color: var(--secondary);
  color: var(--primary);
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.2s ease;

  svg {
    margin-left: 0.5rem;
  }

  &:hover {
    background-color: rgba(100, 255, 218, 0.8);
    transform: translateY(-2px);
  }
`;

export default JobCard;
