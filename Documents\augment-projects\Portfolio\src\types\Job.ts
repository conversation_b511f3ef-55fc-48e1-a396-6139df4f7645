export interface Job {
  job_id: string;
  employer_name: string;
  employer_logo?: string;
  job_title: string;
  job_description: string;
  job_country: string;
  job_city?: string;
  job_state?: string;
  job_apply_link: string;
  job_employment_type: string;
  job_salary?: string;
  job_posted_at_datetime_utc: string;
  job_is_remote: boolean;
  source: string; // Which job board this came from
  saved?: boolean;
}

export interface JobSearchParams {
  query: string;
  page?: number;
  num_pages?: number;
  employment_types?: string[];
  job_requirements?: string[];
  date_posted?: string;
  remote_jobs_only?: boolean;
  country?: string;
}

export interface JobSearchResponse {
  status: string;
  data: Job[];
  num_results: number;
}
