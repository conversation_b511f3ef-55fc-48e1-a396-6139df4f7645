import { useEffect } from 'react';
import { ThemeProvider } from 'styled-components';

// Styles
import GlobalStyles from './styles/GlobalStyles';
import theme from './styles/theme';

// Components
import Navbar from './components/Navbar';

// Sections
import Hero from './sections/Hero';

function IncrementalApp() {
  // Add debug logging
  console.log('IncrementalApp component initializing');
  
  // Add a class to the body when the component mounts
  useEffect(() => {
    console.log('IncrementalApp component mounted');
    document.body.classList.add('bg-grid-pattern');

    return () => {
      document.body.classList.remove('bg-grid-pattern');
    };
  }, []);

  return (
    <ThemeProvider theme={theme}>
      <GlobalStyles />
      <div style={{ position: 'relative', display: 'flex', flexDirection: 'column', alignItems: 'center', width: '100%' }}>
        <Navbar />

        <main style={{ width: '100%', maxWidth: '1200px', margin: '0 auto' }}>
          <Hero />
        </main>
      </div>
    </ThemeProvider>
  );
}

export default IncrementalApp;
