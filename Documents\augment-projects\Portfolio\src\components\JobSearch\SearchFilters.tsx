import React, { useState } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { FiSearch, FiMapPin, FiFilter, FiX, FiCheck } from 'react-icons/fi';
import { useJobContext } from '../../context/JobContext';
import { JobSearchParams } from '../../types/Job';

const SearchFilters: React.FC = () => {
  const { searchParams, setSearchParams } = useJobContext();
  const [localParams, setLocalParams] = useState<JobSearchParams>(searchParams);
  const [showFilters, setShowFilters] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setLocalParams(prev => ({ ...prev, [name]: value }));
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setLocalParams(prev => ({ ...prev, [name]: checked }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setSearchParams(localParams);
  };

  const handleReset = () => {
    const defaultParams: JobSearchParams = {
      query: '',
      page: 1,
      num_pages: 1,
    };
    setLocalParams(defaultParams);
    setSearchParams(defaultParams);
  };

  const employmentTypes = [
    { value: 'FULLTIME', label: 'Full-time' },
    { value: 'PARTTIME', label: 'Part-time' },
    { value: 'CONTRACTOR', label: 'Contractor' },
    { value: 'INTERN', label: 'Internship' },
  ];

  const datePostedOptions = [
    { value: 'all', label: 'Any time' },
    { value: 'today', label: 'Today' },
    { value: 'week', label: 'Past week' },
    { value: 'month', label: 'Past month' },
  ];

  return (
    <SearchContainer>
      <SearchForm onSubmit={handleSubmit}>
        <SearchInputGroup>
          <SearchIcon>
            <FiSearch />
          </SearchIcon>
          <SearchInput
            type="text"
            name="query"
            placeholder="Job title, keywords, or company"
            value={localParams.query || ''}
            onChange={handleInputChange}
          />
        </SearchInputGroup>

        <LocationInputGroup>
          <LocationIcon>
            <FiMapPin />
          </LocationIcon>
          <LocationInput
            type="text"
            name="country"
            placeholder="Country (e.g., USA, Canada)"
            value={localParams.country || ''}
            onChange={handleInputChange}
          />
        </LocationInputGroup>

        <FilterToggle
          type="button"
          onClick={() => setShowFilters(!showFilters)}
          active={showFilters}
        >
          <FiFilter />
          <span>Filters</span>
        </FilterToggle>

        <SearchButton type="submit">
          Search
        </SearchButton>
      </SearchForm>

      {showFilters && (
        <FiltersContainer
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
        >
          <FilterSection>
            <FilterTitle>Employment Type</FilterTitle>
            <CheckboxGroup>
              {employmentTypes.map(type => (
                <CheckboxLabel key={type.value}>
                  <Checkbox
                    type="checkbox"
                    checked={localParams.employment_types?.includes(type.value) || false}
                    onChange={(e) => {
                      const checked = e.target.checked;
                      setLocalParams(prev => ({
                        ...prev,
                        employment_types: checked
                          ? [...(prev.employment_types || []), type.value]
                          : (prev.employment_types || []).filter(t => t !== type.value)
                      }));
                    }}
                  />
                  <CheckboxCustom>
                    {localParams.employment_types?.includes(type.value) && <FiCheck />}
                  </CheckboxCustom>
                  {type.label}
                </CheckboxLabel>
              ))}
            </CheckboxGroup>
          </FilterSection>

          <FilterSection>
            <FilterTitle>Date Posted</FilterTitle>
            <RadioGroup>
              {datePostedOptions.map(option => (
                <RadioLabel key={option.value}>
                  <RadioInput
                    type="radio"
                    name="date_posted"
                    value={option.value}
                    checked={localParams.date_posted === option.value}
                    onChange={() => setLocalParams(prev => ({ ...prev, date_posted: option.value }))}
                  />
                  <RadioCustom>
                    {localParams.date_posted === option.value && <RadioDot />}
                  </RadioCustom>
                  {option.label}
                </RadioLabel>
              ))}
            </RadioGroup>
          </FilterSection>

          <FilterSection>
            <FilterTitle>Other Filters</FilterTitle>
            <CheckboxLabel>
              <Checkbox
                type="checkbox"
                name="remote_jobs_only"
                checked={localParams.remote_jobs_only || false}
                onChange={handleCheckboxChange}
              />
              <CheckboxCustom>
                {localParams.remote_jobs_only && <FiCheck />}
              </CheckboxCustom>
              Remote jobs only
            </CheckboxLabel>
          </FilterSection>

          <FilterActions>
            <ResetButton type="button" onClick={handleReset}>
              <FiX />
              Reset Filters
            </ResetButton>
            <ApplyButton type="button" onClick={handleSubmit}>
              Apply Filters
            </ApplyButton>
          </FilterActions>
        </FiltersContainer>
      )}
    </SearchContainer>
  );
};

const SearchContainer = styled.div`
  background-color: var(--primary);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
`;

const SearchForm = styled.form`
  display: grid;
  grid-template-columns: 1fr 1fr auto auto;
  gap: 1rem;
  align-items: center;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const SearchInputGroup = styled.div`
  position: relative;
  flex: 2;
`;

const SearchIcon = styled.div`
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--tertiary);
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid var(--tertiary);
  border-radius: 4px;
  background-color: transparent;
  color: var(--light);
  font-size: 1rem;

  &:focus {
    outline: none;
    border-color: var(--secondary);
  }

  &::placeholder {
    color: var(--tertiary);
  }
`;

const LocationInputGroup = styled.div`
  position: relative;
  flex: 1;
`;

const LocationIcon = styled.div`
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--tertiary);
`;

const LocationInput = styled.input`
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid var(--tertiary);
  border-radius: 4px;
  background-color: transparent;
  color: var(--light);
  font-size: 1rem;

  &:focus {
    outline: none;
    border-color: var(--secondary);
  }

  &::placeholder {
    color: var(--tertiary);
  }
`;

interface FilterToggleProps {
  active: boolean;
}

const FilterToggle = styled.button<FilterToggleProps>`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 1px solid ${props => props.active ? 'var(--secondary)' : 'var(--tertiary)'};
  border-radius: 4px;
  background-color: transparent;
  color: ${props => props.active ? 'var(--secondary)' : 'var(--tertiary)'};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: var(--secondary);
    color: var(--secondary);
  }
`;

const SearchButton = styled.button`
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  background-color: var(--secondary);
  color: var(--primary);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(100, 255, 218, 0.8);
  }
`;

const FiltersContainer = styled(motion.div)`
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(137, 146, 176, 0.2);
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const FilterSection = styled.div``;

const FilterTitle = styled.h4`
  margin: 0 0 1rem;
  color: var(--light);
  font-size: 1rem;
`;

const CheckboxGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
`;

const CheckboxLabel = styled.label`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--tertiary);
  cursor: pointer;
  font-size: 0.9rem;
  transition: color 0.2s ease;

  &:hover {
    color: var(--light);
  }
`;

const Checkbox = styled.input`
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
`;

const CheckboxCustom = styled.div`
  width: 18px;
  height: 18px;
  border: 1px solid var(--tertiary);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  svg {
    color: var(--secondary);
    font-size: 0.8rem;
  }

  ${CheckboxLabel}:hover & {
    border-color: var(--secondary);
  }
`;

const RadioGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
`;

const RadioLabel = styled.label`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--tertiary);
  cursor: pointer;
  font-size: 0.9rem;
  transition: color 0.2s ease;

  &:hover {
    color: var(--light);
  }
`;

const RadioInput = styled.input`
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
`;

const RadioCustom = styled.div`
  width: 18px;
  height: 18px;
  border: 1px solid var(--tertiary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  ${RadioLabel}:hover & {
    border-color: var(--secondary);
  }
`;

const RadioDot = styled.div`
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: var(--secondary);
`;

const FilterActions = styled.div`
  grid-column: 1 / -1;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(137, 146, 176, 0.2);
`;

const ResetButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid var(--tertiary);
  border-radius: 4px;
  background-color: transparent;
  color: var(--tertiary);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: var(--secondary);
    color: var(--secondary);
  }
`;

const ApplyButton = styled.button`
  padding: 0.5rem 1.5rem;
  border: none;
  border-radius: 4px;
  background-color: var(--secondary);
  color: var(--primary);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(100, 255, 218, 0.8);
  }
`;

export default SearchFilters;
