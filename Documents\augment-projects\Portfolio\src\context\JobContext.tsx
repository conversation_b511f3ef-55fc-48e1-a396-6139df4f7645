import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Job, JobSearchParams } from '../types/Job';
import { fetchJobsFromMultipleSources } from '../services/jobApi';
import { getSavedJobs, markSavedJobs } from '../services/jobStorage';

interface JobContextType {
  jobs: Job[];
  savedJobs: Job[];
  loading: boolean;
  error: string | null;
  searchParams: JobSearchParams;
  setSearchParams: (params: JobSearchParams) => void;
  refreshJobs: () => Promise<void>;
  selectedJob: Job | null;
  setSelectedJob: (job: Job | null) => void;
}

const JobContext = createContext<JobContextType | undefined>(undefined);

export const JobProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [jobs, setJobs] = useState<Job[]>([]);
  const [savedJobs, setSavedJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [searchParams, setSearchParams] = useState<JobSearchParams>({
    query: 'software developer',
    page: 1,
    num_pages: 1,
  });
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);

  // Load saved jobs from local storage
  useEffect(() => {
    setSavedJobs(getSavedJobs());
  }, []);

  // Refresh jobs when search parameters change
  useEffect(() => {
    refreshJobs();
  }, [searchParams]);

  // Set up polling for real-time updates (every 5 minutes)
  useEffect(() => {
    const interval = setInterval(() => {
      refreshJobs();
    }, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, [searchParams]);

  const refreshJobs = async (): Promise<void> => {
    setLoading(true);
    setError(null);
    
    try {
      const fetchedJobs = await fetchJobsFromMultipleSources(
        searchParams.query,
        searchParams.page
      );
      
      // Mark saved jobs
      const markedJobs = markSavedJobs(fetchedJobs);
      setJobs(markedJobs);
    } catch (err) {
      setError('Failed to fetch jobs. Please try again later.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <JobContext.Provider
      value={{
        jobs,
        savedJobs,
        loading,
        error,
        searchParams,
        setSearchParams,
        refreshJobs,
        selectedJob,
        setSelectedJob,
      }}
    >
      {children}
    </JobContext.Provider>
  );
};

export const useJobContext = (): JobContextType => {
  const context = useContext(JobContext);
  if (context === undefined) {
    throw new Error('useJobContext must be used within a JobProvider');
  }
  return context;
};
