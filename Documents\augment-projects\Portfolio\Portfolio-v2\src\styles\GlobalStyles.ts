import { createGlobalStyle } from 'styled-components';

const GlobalStyles = createGlobalStyle`
  :root {
    --primary: #0a192f;
    --secondary: #64ffda;
    --tertiary: #8892b0;
    --light: #ccd6f6;
    --dark: #020c1b;
  }

  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Inter', sans-serif;
    background-color: var(--primary);
    color: var(--light);
    overflow-x: hidden;
    line-height: 1.6;
  }

  h1, h2, h3, h4, h5, h6 {
    margin-bottom: 1rem;
    line-height: 1.2;
  }

  p {
    margin-bottom: 1rem;
  }

  a {
    color: var(--secondary);
    text-decoration: none;
    transition: all 0.3s ease;
  }

  a:hover {
    color: var(--light);
  }

  button {
    cursor: pointer;
    border: none;
    outline: none;
    background: none;
    font-family: inherit;
  }

  /* Custom cursor */
  .custom-cursor {
    pointer-events: none;
    z-index: 9999;
  }

  /* Utility classes */
  .text-primary {
    color: var(--primary);
  }

  .text-secondary {
    color: var(--secondary);
  }

  .text-tertiary {
    color: var(--tertiary);
  }

  .text-light {
    color: var(--light);
  }

  .font-mono {
    font-family: 'Roboto Mono', monospace;
  }

  /* Glow effect for active elements */
  .glow-effect {
    box-shadow: 0 0 15px rgba(100, 255, 218, 0.5);
    position: relative;
    z-index: 1;
  }
`;

export default GlobalStyles;
