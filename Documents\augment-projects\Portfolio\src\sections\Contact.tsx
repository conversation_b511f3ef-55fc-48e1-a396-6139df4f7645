import { useState } from 'react';
import { motion } from 'framer-motion';
import SectionTitle from '../components/SectionTitle';
import FadeInSection from '../components/FadeInSection';
import { FiMail, FiPhone, FiLinkedin, FiGithub, FiMessageCircle } from 'react-icons/fi';

const Contact = () => {
  const [activeContact, setActiveContact] = useState<string | null>(null);

  const contactOptions = [
    {
      id: 'email',
      icon: <FiMail className="text-xl" />,
      label: 'Email',
      description: 'Fastest way to reach me for job opportunities',
      action: 'mailto:<EMAIL>?subject=Job%20Opportunity',
      value: '<EMAIL>'
    },
    {
      id: 'linkedin',
      icon: <FiLinkedin className="text-xl" />,
      label: 'LinkedIn',
      description: 'Connect with me professionally',
      action: 'https://www.linkedin.com/in/mdhlee/',
      value: 'linkedin.com/in/mdhlee'
    },
    {
      id: 'phone',
      icon: <FiPhone className="text-xl" />,
      label: 'Phone',
      description: 'Direct line for urgent matters',
      action: 'tel:+16107418676',
      value: '+****************'
    },
    {
      id: 'github',
      icon: <FiGithub className="text-xl" />,
      label: 'GitHub',
      description: 'Check out my code and projects',
      action: 'https://github.com/MikeDominic92',
      value: 'github.com/MikeDominic92'
    }
  ];

  const handleContactClick = (id: string) => {
    setActiveContact(activeContact === id ? null : id);
  };

  return (
    <section id="contact" className="py-20 bg-primary">
      <div className="section-container">
        <SectionTitle number="05" title="Get In Touch" />

        <div className="w-full max-w-4xl mx-auto">
          <FadeInSection delay={0.2}>
            <div className="text-center space-y-6 mb-10">
              <h3 className="text-3xl font-bold text-light">Let's Connect</h3>
              <p className="text-tertiary max-w-2xl mx-auto">
                I'm always interested in discussing new opportunities in cloud engineering, security, and DevOps.
                Select your preferred contact method below for the fastest way to reach me.
              </p>
            </div>

            <div className="flex flex-wrap justify-center gap-12 mb-10">
              {contactOptions.map((option) => (
                <motion.button
                  key={option.id}
                  onClick={() => handleContactClick(option.id)}
                  className={`flex items-center gap-3 px-6 py-3 rounded-full border-2 transition-all ${activeContact === option.id
                    ? 'bg-secondary/20 border-secondary text-secondary'
                    : 'border-secondary/30 text-light hover:border-secondary/60'}`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <span className="text-secondary">{option.icon}</span>
                  <span>{option.label}</span>
                </motion.button>
              ))}
            </div>

            {activeContact && (
              <FadeInSection delay={0.1}>
                <div className="bg-secondary/5 border border-secondary/20 rounded-lg p-6 max-w-2xl mx-auto">
                  {contactOptions.map((option) => (
                    option.id === activeContact && (
                      <div key={option.id} className="text-center">
                        <div className="w-16 h-16 rounded-full bg-secondary/10 flex items-center justify-center mx-auto mb-4">
                          <span className="text-secondary text-2xl">{option.icon}</span>
                        </div>
                        <h4 className="text-xl font-bold text-light mb-2">{option.label}</h4>
                        <p className="text-tertiary mb-4">{option.description}</p>
                        <div className="flex items-center justify-center gap-2 mb-6">
                          <span className="text-light">{option.value}</span>
                          <button
                            onClick={() => navigator.clipboard.writeText(option.value)}
                            className="text-secondary hover:text-secondary/80 text-sm"
                          >
                            Copy
                          </button>
                        </div>
                        <a
                          href={option.action}
                          target={option.id === 'linkedin' || option.id === 'github' ? '_blank' : undefined}
                          rel={option.id === 'linkedin' || option.id === 'github' ? 'noopener noreferrer' : undefined}
                          className="btn-primary inline-flex items-center justify-center gap-2"
                        >
                          <FiMessageCircle />
                          {option.id === 'email' ? 'Send Email' :
                           option.id === 'linkedin' ? 'Message on LinkedIn' :
                           option.id === 'phone' ? 'Call Now' :
                           'View Profile'}
                        </a>
                      </div>
                    )
                  ))}
                </div>
              </FadeInSection>
            )}
          </FadeInSection>
        </div>
      </div>
    </section>
  );
};

export default Contact;
