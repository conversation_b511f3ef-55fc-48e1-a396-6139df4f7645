import { useEffect } from 'react';
import { ThemeProvider } from 'styled-components';

// Styles
import GlobalStyles from './styles/GlobalStyles';
import theme from './styles/theme';

// Components
import Navbar from './components/Navbar';
import CustomCursor from './components/CustomCursor';
import ScrollProgress from './components/ScrollProgress';
import Footer from './components/Footer';
import TopSocialIcons from './components/TopSocialIcons';
import BackToTop from './components/BackToTop';
import TopRightIcons from './components/TopRightIcons';
import AutoScroll from './components/AutoScroll';

// Sections
import Hero from './sections/Hero';
import About from './sections/About';
import Skills from './sections/Skills';
import Experience from './sections/Experience';
import Projects from './sections/Projects';
import Certifications from './sections/Certifications';
import JobSearch from './sections/JobSearch';
import Contact from './sections/Contact';

function App() {
  // Add debug logging
  console.log('App component initializing');

  // Add a class to the body when the component mounts
  useEffect(() => {
    console.log('App component mounted');
    document.body.classList.add('bg-grid-pattern');

    return () => {
      document.body.classList.remove('bg-grid-pattern');
    };
  }, []);

  return (
    <ThemeProvider theme={theme}>
      <GlobalStyles />
      <div style={{ position: 'relative', display: 'flex', flexDirection: 'column', alignItems: 'center', width: '100%' }}>
        <CustomCursor />
        <ScrollProgress />
        <TopSocialIcons />
        <Navbar />
        <BackToTop />
        <TopRightIcons />
        <AutoScroll />

        <main style={{ width: '100%', maxWidth: '1200px', margin: '0 auto' }}>
          <Hero />
          <About />
          <Skills />
          <Experience />
          <Projects />
          <Certifications />
          <JobSearch />
          <Contact />
        </main>
        <Footer />
      </div>
    </ThemeProvider>
  );
}

export default App;
