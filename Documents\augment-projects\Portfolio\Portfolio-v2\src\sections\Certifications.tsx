import { motion } from 'framer-motion';
import SectionTitle from '../components/SectionTitle';
import FadeInSection from '../components/FadeInSection';

const Certifications = () => {
  const certifications = [
    {
      name: 'Google Cloud Certified – Professional Cloud Architect (GCP PCA)',
      issuer: 'Google Cloud',
      icon: '/icons/gcp.svg',
      color: '#4285F4'
    },
    {
      name: 'Cisco Certified Network Associate (CCNA)',
      issuer: 'Cisco',
      icon: '/icons/cisco.svg',
      color: '#1BA0D7'
    },
    {
      name: 'CompTIA Security+',
      issuer: 'CompTIA',
      icon: '/icons/comptia.svg',
      color: '#C8202B'
    },
    {
      name: 'AWS Certified Cloud Practitioner (AWS CCP)',
      issuer: 'Amazon Web Services',
      icon: '/icons/aws.svg',
      color: '#FF9900'
    },
    {
      name: 'AWS Certified Solutions Architect – Associate (AWS SAA)',
      issuer: 'Amazon Web Services',
      icon: '/icons/aws.svg',
      color: '#FF9900'
    },
    {
      name: 'Chainguard Painless Vulnerability Management & Securing the AI/ML Supply Chain',
      issuer: 'Chainguard',
      icon: '/icons/chainguard.svg',
      color: '#6F43E7'
    },
    {
      name: 'Google Cloud Cybersecurity Specialization',
      issuer: 'Google Cloud',
      icon: '/icons/gcp.svg',
      color: '#4285F4'
    },
    {
      name: 'Google Cybersecurity Specialization',
      issuer: 'Google',
      icon: '/icons/google.svg',
      color: '#4285F4'
    },
    {
      name: 'Google IT Support Specialization',
      issuer: 'Google',
      icon: '/icons/google.svg',
      color: '#4285F4'
    },
    {
      name: 'Google Cloud Data Analytics Specialization',
      issuer: 'Google Cloud',
      icon: '/icons/gcp.svg',
      color: '#4285F4'
    },
    {
      name: 'Google Advance Data Analytics Specialization',
      issuer: 'Google',
      icon: '/icons/google.svg',
      color: '#4285F4'
    }
  ];

  return (
    <section id="certifications" className="py-20 bg-primary/95">
      <div className="section-container">
        <SectionTitle number="04" title="Certifications" />

        <div className="flex flex-wrap justify-center gap-6 w-full">
          {certifications.map((cert, index) => (
            <FadeInSection key={cert.name} delay={0.1 + index * 0.05}>
              <motion.div
                className="bg-dark p-4 rounded-lg border border-tertiary/20 hover:border-secondary/50 transition-all duration-300 w-[280px]"
                whileHover={{ y: -5, boxShadow: `0 10px 30px -15px ${cert.color}20` }}
              >
                <div className="flex items-start mb-4">
                  <div className="w-12 h-12 mr-4 flex items-center justify-center bg-primary/50 rounded-md border border-tertiary/20">
                    <div
                      className="w-8 h-8 rounded-full"
                      style={{ backgroundColor: `${cert.color}20` }}
                    >
                      {/* Placeholder for icon */}
                    </div>
                  </div>
                  <div>
                    <h3 className="text-light font-bold">{cert.name}</h3>
                    <p className="text-tertiary text-sm">{cert.issuer}</p>
                  </div>
                </div>
              </motion.div>
            </FadeInSection>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Certifications;
