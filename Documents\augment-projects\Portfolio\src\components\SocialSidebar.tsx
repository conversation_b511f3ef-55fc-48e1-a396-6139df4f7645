import { motion } from 'framer-motion';
import { FiGithub, FiLinkedin, FiMail } from 'react-icons/fi';

const SocialSidebar = () => {
  return (
    <motion.div
      className="fixed left-6 bottom-0 hidden md:flex flex-col items-center z-[100]"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5, delay: 1.5 }}
    >
      <div className="flex flex-col gap-6 mb-6">
        <a
          href="https://github.com/MikeDominic92"
          target="_blank"
          rel="noopener noreferrer"
          className="text-tertiary hover:text-secondary hover:-translate-y-1 transition-all duration-300"
          aria-label="GitHub"
        >
          <FiGithub size={20} />
        </a>
        <a
          href="https://www.linkedin.com/in/mdhlee/"
          target="_blank"
          rel="noopener noreferrer"
          className="text-tertiary hover:text-secondary hover:-translate-y-1 transition-all duration-300"
          aria-label="LinkedIn"
        >
          <FiLinkedin size={20} />
        </a>
        <a
          href="mailto:<EMAIL>"
          className="text-tertiary hover:text-secondary hover:-translate-y-1 transition-all duration-300"
          aria-label="Email"
        >
          <FiMail size={20} />
        </a>
      </div>
      <div className="w-px h-24 bg-tertiary"></div>
    </motion.div>
  );
};

export default SocialSidebar;
