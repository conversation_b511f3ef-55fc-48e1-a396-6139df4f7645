import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiCheck, FiChevronDown, FiChevronRight } from 'react-icons/fi';
import SectionTitle from '../components/SectionTitle';
import FadeInSection from '../components/FadeInSection';

type Skill = {
  name: string;
  description: string;
  dailyUse: string;
  projectReference?: string;
};

type SkillCategory = {
  id: string;
  name: string;
  icon: string;
  description: string;
  skills: Skill[];
};

const Skills = () => {
  const [activeCategory, setActiveCategory] = useState('cloud');
  const [expandedSkill, setExpandedSkill] = useState<string | null>(null);
  const [autoRotate, setAutoRotate] = useState(true);

  const rotationRef = useRef<NodeJS.Timeout | null>(null);

  const toggleSkill = (skillName: string) => {
    if (expandedSkill === skillName) {
      setExpandedSkill(null);
    } else {
      setExpandedSkill(skillName);
    }
  };

  // Auto-rotate through categories and skills
  useEffect(() => {
    if (!autoRotate) return;

    // Clear any existing interval
    if (rotationRef.current) {
      clearTimeout(rotationRef.current);
    }

    const currentCategory = categories.find(cat => cat.id === activeCategory);
    if (!currentCategory) return;

    // If no skill is expanded, show the first one
    if (!expandedSkill) {
      // Expand the first skill after a delay
      rotationRef.current = setTimeout(() => {
        if (currentCategory.skills.length > 0) {
          setExpandedSkill(currentCategory.skills[0].name);
        }
      }, 1000); // Wait for 1 second
      return;
    }

    // Find the index of the current expanded skill
    const currentSkillIndex = currentCategory.skills.findIndex(skill => skill.name === expandedSkill);

    // If we've reached the last skill in this category
    if (currentSkillIndex === currentCategory.skills.length - 1) {
      // Close the current skill
      rotationRef.current = setTimeout(() => {
        setExpandedSkill(null);

        // After closing, move to the next category
        setTimeout(() => {
          const categoryIndex = categories.findIndex(cat => cat.id === activeCategory);
          const nextCategoryIndex = (categoryIndex + 1) % categories.length;
          setActiveCategory(categories[nextCategoryIndex].id);
        }, 1000);
      }, 10000); // Show the last skill for 10 seconds before moving on
    } else {
      // Move to the next skill in the current category
      rotationRef.current = setTimeout(() => {
        setExpandedSkill(null); // First close current skill

        // Then open the next skill after a short delay
        setTimeout(() => {
          if (currentSkillIndex + 1 < currentCategory.skills.length) {
            setExpandedSkill(currentCategory.skills[currentSkillIndex + 1].name);
          }
        }, 1000);
      }, 10000); // Show each skill for 10 seconds
    }

    return () => {
      if (rotationRef.current) {
        clearTimeout(rotationRef.current);
      }
    };
  }, [activeCategory, expandedSkill, autoRotate]);

  const categories: SkillCategory[] = [
    {
      id: 'cloud',
      name: 'Cloud',
      icon: '☁️',
      description: 'I architect and maintain multi-cloud environments, focusing on scalability, cost optimization, and high availability.',
      skills: [
        {
          name: 'AWS',
          description: 'Expert in AWS services including EC2, S3, Lambda, RDS, and IAM.',
          dailyUse: 'At AlgOPro Solutions, I manage our AWS infrastructure that supports high-frequency trading systems. I have implemented auto-scaling solutions that reduced our compute costs by 30% while maintaining sub-millisecond response times.',
          projectReference: 'My Predictive Threat Intelligence Platform leverages AWS services to analyze and predict potential security vulnerabilities.'
        },
        {
          name: 'GCP',
          description: 'Proficient with Google Cloud Platform services and architecture.',
          dailyUse: 'I work with GCP daily to manage our secondary cloud infrastructure, particularly focusing on BigQuery for data analytics and Cloud Functions for serverless operations.',
          projectReference: 'Developed a dedicated Cloud Security AI Monitor specifically for Google Cloud Platform that continuously scans for security anomalies.'
        },
        {
          name: 'Infrastructure as Code',
          description: 'Expertise in automating infrastructure deployment using Terraform and CloudFormation.',
          dailyUse: 'I maintain our infrastructure as code repositories, ensuring all cloud resources are version-controlled and consistently deployed across environments.',
          projectReference: 'Both my cloud security projects use IaC principles to ensure consistent, repeatable deployments.'
        },
        {
          name: 'Cloud Architecture',
          description: 'Designing resilient, scalable cloud architectures across multiple platforms.',
          dailyUse: 'I regularly design and implement cloud architectures that balance performance, security, and cost-effectiveness for our trading platforms.',
        }
      ]
    },
    {
      id: 'security',
      name: 'Security',
      icon: '🔒',
      description: 'I implement robust security measures to protect cloud infrastructure, with a focus on automation and proactive threat detection.',
      skills: [
        {
          name: 'Cloud Security',
          description: 'Implementing security best practices across cloud environments.',
          dailyUse: 'I conduct daily security audits of our cloud infrastructure, implementing least-privilege access controls and ensuring compliance with financial industry regulations.',
          projectReference: 'My Predictive Threat Intelligence Platform uses AI to identify potential vulnerabilities before they can be exploited.'
        },
        {
          name: 'Vulnerability Assessment',
          description: 'Identifying and remediating security vulnerabilities in cloud infrastructure.',
          dailyUse: 'I run automated vulnerability scans across our infrastructure and lead remediation efforts for any identified issues.',
          projectReference: 'Developed custom vulnerability assessment tools that integrate with our CI/CD pipeline.'
        },
        {
          name: 'Security Automation',
          description: 'Building automated security controls and monitoring systems.',
          dailyUse: 'I have created automated security response systems that can detect and mitigate certain types of attacks without human intervention.',
          projectReference: 'Both my GitHub security projects leverage automation to continuously monitor and protect cloud resources.'
        },
        {
          name: 'Compliance',
          description: 'Ensuring systems meet regulatory and industry security standards.',
          dailyUse: 'I work closely with our compliance team to ensure our systems meet SOC 2 and financial industry requirements.',
        }
      ]
    },
    {
      id: 'support',
      name: 'Support',
      icon: '🛠️',
      description: 'I provide expert-level technical support, focusing on root cause analysis and permanent solutions rather than quick fixes.',
      skills: [
        {
          name: 'Incident Management',
          description: 'Leading incident response and resolution processes.',
          dailyUse: 'I serve as the primary incident commander for critical production issues, coordinating response efforts and ensuring timely resolution.',
          projectReference: 'My Advanced Ticket System includes sophisticated incident management workflows based on my real-world experience.'
        },
        {
          name: 'Technical Troubleshooting',
          description: 'Advanced troubleshooting of complex technical issues across the stack.',
          dailyUse: 'I regularly diagnose and resolve complex issues spanning our entire technology stack, from network to application layers.',
        },
        {
          name: 'Knowledge Management',
          description: 'Creating and maintaining technical documentation and knowledge bases.',
          dailyUse: 'I have established our internal knowledge base and runbooks, reducing mean time to resolution by 40%.',
          projectReference: 'The Advanced Ticket System I developed includes integrated knowledge management features.'
        },
        {
          name: 'Customer Communication',
          description: 'Effectively communicating technical concepts to non-technical stakeholders.',
          dailyUse: 'I regularly translate complex technical issues into clear updates for our trading desk and executive team.',
        }
      ]
    },
    {
      id: 'devops',
      name: 'DevOps',
      icon: '🚀',
      description: 'I implement DevOps practices that bridge development and operations, focusing on automation, CI/CD, and observability.',
      skills: [
        {
          name: 'CI/CD Pipelines',
          description: 'Building and maintaining continuous integration and deployment pipelines.',
          dailyUse: 'I maintain our CI/CD pipelines that enable us to safely deploy multiple times per day to our production environments.',
          projectReference: 'All my GitHub projects use CI/CD principles for automated testing and deployment.'
        },
        {
          name: 'Infrastructure Automation',
          description: 'Automating infrastructure provisioning and configuration management.',
          dailyUse: 'I have automated our entire infrastructure provisioning process, reducing new environment setup time from days to hours.',
        },
        {
          name: 'Monitoring & Observability',
          description: 'Implementing comprehensive monitoring and observability solutions.',
          dailyUse: 'I maintain our observability stack, ensuring we have complete visibility into system performance and behavior.',
          projectReference: 'My Cloud Security AI Monitor includes advanced observability features for detecting anomalous behavior.'
        },
        {
          name: 'Containerization',
          description: 'Expertise in Docker, Kubernetes, and container orchestration.',
          dailyUse: 'I manage our Kubernetes clusters that run our microservices architecture, ensuring high availability and efficient resource utilization.',
        }
      ]
    },
    {
      id: 'opensource',
      name: 'Open Source',
      icon: '⚡',
      description: 'I actively contribute to and leverage open source technologies, believing in the power of community-driven innovation.',
      skills: [
        {
          name: 'Open Source Contributions',
          description: 'Contributing to open source projects in the cloud and security space.',
          dailyUse: 'I regularly contribute to open source tools we use internally, submitting bug fixes and feature enhancements.',
          projectReference: 'All my GitHub projects are open source, allowing others to learn from and build upon my work.'
        },
        {
          name: 'Community Engagement',
          description: 'Actively participating in technical communities and knowledge sharing.',
          dailyUse: 'I participate in cloud security forums and contribute to discussions about best practices and emerging threats.',
        },
        {
          name: 'Open Source Integration',
          description: 'Integrating and extending open source tools for enterprise use.',
          dailyUse: 'I have integrated multiple open source monitoring tools into our observability platform, extending them to meet our specific needs.',
          projectReference: 'My security projects leverage and extend several open source security tools.'
        },
        {
          name: 'Technology Evaluation',
          description: 'Evaluating emerging open source technologies for potential adoption.',
          dailyUse: 'I regularly evaluate new open source tools and technologies, running proof of concepts to determine their potential value to our organization.',
        }
      ]
    },
  ];

  const activeSkillSet = categories.find(category => category.id === activeCategory) || categories[0];

  return (
    <section id="skills" className="py-20 bg-primary/95">
      <div className="section-container">
        <SectionTitle number="02" title="Skills & Expertise" />

        <FadeInSection delay={0.2}>
          <div className="flex flex-wrap justify-center gap-6 mb-16 w-full max-w-4xl mx-auto">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => {
                  setAutoRotate(false); // Stop auto-rotation when user clicks
                  setActiveCategory(category.id);
                }}
                className={`px-8 py-4 rounded-lg text-base font-medium transition-all duration-300 flex items-center gap-3 ${
                  activeCategory === category.id
                    ? 'bg-secondary/40 text-white border-2 border-secondary shadow-xl shadow-secondary/30 scale-115 transform -translate-y-1 glow-effect'
                    : 'bg-dark/70 text-tertiary hover:bg-dark/90 border border-tertiary/30 hover:border-tertiary/50 hover:scale-105'
                }`}
              >
                <span className={`text-2xl mr-1 ${activeCategory === category.id ? 'text-secondary' : 'text-tertiary'}`}>{category.icon}</span>
                <span className={`relative ${activeCategory === category.id ? 'text-white' : 'text-tertiary'}`}>{category.name}
                {activeCategory === category.id && <span className="absolute -bottom-1 left-0 w-full h-0.5 bg-secondary"></span>}</span>
              </button>
            ))}
          </div>
        </FadeInSection>

        <div className="mb-12 w-full">
          <FadeInSection delay={0.3}>
            <div className="p-8 bg-dark/30 border-2 border-secondary/40 rounded-lg shadow-xl shadow-secondary/20 relative overflow-hidden">
              <div className="absolute top-0 left-0 w-full h-2 bg-secondary/70"></div>
              <div className="absolute top-2 right-2 w-3 h-3 rounded-full bg-secondary animate-pulse"></div>
              <div className="flex items-center gap-3 mb-6 bg-secondary/10 p-3 rounded-lg border border-secondary/30">
                <span className="text-3xl text-secondary">{activeSkillSet.icon}</span>
                <h3 className="text-2xl font-bold text-white">{activeSkillSet.name}</h3>
              </div>

              <p className="text-tertiary mb-8 text-lg">{activeSkillSet.description}</p>

              <div className="space-y-4">
                {activeSkillSet.skills.map((skill) => (
                  <div
                    key={skill.name}
                    className={`border ${expandedSkill === skill.name ? 'border-secondary' : 'border-secondary/10'} rounded-lg overflow-hidden ${expandedSkill === skill.name ? 'bg-secondary/10 glow-effect' : 'bg-dark/20'} transition-all duration-300`}
                  >
                    <button
                      onClick={() => {
                        setAutoRotate(false); // Stop auto-rotation when user clicks
                        toggleSkill(skill.name);
                      }}
                      className="w-full p-4 flex items-center justify-between text-left hover:bg-secondary/5 transition-colors"
                    >
                      <div className="flex items-center gap-3">
                        <span className={`font-medium transition-colors duration-300 ${expandedSkill === skill.name ? 'text-white' : 'text-tertiary'}`}>{skill.name}</span>
                      </div>
                      <div className="text-secondary">
                        {expandedSkill === skill.name ? <FiChevronDown size={18} /> : <FiChevronRight size={18} />}
                      </div>
                    </button>

                    <AnimatePresence>
                      {expandedSkill === skill.name && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: 'auto', opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3 }}
                          className="overflow-hidden"
                        >
                          <div className="p-4 pt-0 border-t border-secondary/10">
                            <p className="text-white mb-3">{skill.description}</p>

                            <div className="bg-secondary/5 p-3 rounded-md mb-3">
                              <h4 className="text-secondary font-medium mb-2">Daily Application:</h4>
                              <p className="text-white">{skill.dailyUse}</p>
                            </div>

                            {skill.projectReference && (
                              <div className="bg-primary/40 p-3 rounded-md">
                                <h4 className="text-secondary font-medium mb-2">Project Reference:</h4>
                                <p className="text-white">{skill.projectReference}</p>
                              </div>
                            )}
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                ))}
              </div>
            </div>
          </FadeInSection>
        </div>
      </div>
    </section>
  );
};

export default Skills;
