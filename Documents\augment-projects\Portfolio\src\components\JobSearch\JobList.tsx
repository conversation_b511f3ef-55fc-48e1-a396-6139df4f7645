import React, { useState } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { FiChevronLeft, FiChevronRight, FiRefreshCw, FiBookmark } from 'react-icons/fi';
import { useJobContext } from '../../context/JobContext';
import JobCard from './JobCard';
import JobDetail from './JobDetail';

const JobList: React.FC = () => {
  const { jobs, loading, error, refreshJobs, searchParams, setSearchParams, selectedJob } = useJobContext();
  const [showSaved, setShowSaved] = useState(false);

  const handlePageChange = (newPage: number) => {
    setSearchParams({
      ...searchParams,
      page: newPage,
    });
  };

  const filteredJobs = showSaved ? jobs.filter(job => job.saved) : jobs;

  return (
    <JobListContainer>
      <ListHeader>
        <ResultsInfo>
          {loading ? (
            <span>Loading jobs...</span>
          ) : (
            <span>
              {filteredJobs.length} {filteredJobs.length === 1 ? 'job' : 'jobs'} found
              {searchParams.query && ` for "${searchParams.query}"`}
              {searchParams.country && ` in ${searchParams.country}`}
            </span>
          )}
        </ResultsInfo>
        <ListActions>
          <SavedToggle
            onClick={() => setShowSaved(!showSaved)}
            active={showSaved}
          >
            <FiBookmark />
            <span>Saved Jobs</span>
          </SavedToggle>
          <RefreshButton onClick={() => refreshJobs()} disabled={loading}>
            <FiRefreshCw className={loading ? 'spinning' : ''} />
            <span>Refresh</span>
          </RefreshButton>
        </ListActions>
      </ListHeader>

      {error && <ErrorMessage>{error}</ErrorMessage>}

      <ListContent>
        <JobsColumn>
          {loading ? (
            <LoadingContainer>
              {[1, 2, 3].map(i => (
                <SkeletonCard key={i} />
              ))}
            </LoadingContainer>
          ) : filteredJobs.length === 0 ? (
            <NoJobsMessage>
              {showSaved
                ? "You haven't saved any jobs yet."
                : "No jobs found. Try adjusting your search filters."}
            </NoJobsMessage>
          ) : (
            <>
              <AnimatePresence>
                {filteredJobs.map(job => (
                  <JobCard key={job.job_id} job={job} />
                ))}
              </AnimatePresence>

              <Pagination>
                <PaginationButton
                  onClick={() => handlePageChange(Math.max(1, searchParams.page! - 1))}
                  disabled={searchParams.page === 1 || loading}
                >
                  <FiChevronLeft />
                  Previous
                </PaginationButton>
                <PageInfo>
                  Page {searchParams.page} of {searchParams.num_pages || 1}
                </PageInfo>
                <PaginationButton
                  onClick={() => handlePageChange(searchParams.page! + 1)}
                  disabled={loading}
                >
                  Next
                  <FiChevronRight />
                </PaginationButton>
              </Pagination>
            </>
          )}
        </JobsColumn>

        <DetailColumn>
          <AnimatePresence>
            {selectedJob && (
              <motion.div
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 50 }}
                transition={{ duration: 0.3 }}
              >
                <JobDetail job={selectedJob} />
              </motion.div>
            )}
          </AnimatePresence>
        </DetailColumn>
      </ListContent>
    </JobListContainer>
  );
};

const JobListContainer = styled.div`
  width: 100%;
`;

const ListHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
`;

const ResultsInfo = styled.div`
  color: var(--light);
  font-size: 1rem;
`;

const ListActions = styled.div`
  display: flex;
  gap: 1rem;
`;

interface SavedToggleProps {
  active: boolean;
}

const SavedToggle = styled.button<SavedToggleProps>`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid ${props => props.active ? 'var(--secondary)' : 'var(--tertiary)'};
  border-radius: 4px;
  background-color: ${props => props.active ? 'rgba(100, 255, 218, 0.1)' : 'transparent'};
  color: ${props => props.active ? 'var(--secondary)' : 'var(--tertiary)'};
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: var(--secondary);
    color: var(--secondary);
  }
`;

const RefreshButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid var(--tertiary);
  border-radius: 4px;
  background-color: transparent;
  color: var(--tertiary);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    border-color: var(--secondary);
    color: var(--secondary);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .spinning {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
`;

const ListContent = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  
  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
  }
`;

const JobsColumn = styled.div``;

const DetailColumn = styled.div`
  position: sticky;
  top: 2rem;
  height: calc(100vh - 4rem);
  overflow-y: auto;
  
  @media (max-width: 1024px) {
    display: none;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const SkeletonCard = styled.div`
  background-color: var(--primary);
  border: 1px solid var(--tertiary);
  border-radius: 8px;
  padding: 1.5rem;
  height: 200px;
  position: relative;
  overflow: hidden;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      rgba(137, 146, 176, 0.05) 25%,
      rgba(137, 146, 176, 0.1) 50%,
      rgba(137, 146, 176, 0.05) 75%
    );
    animation: loading 1.5s infinite;
  }
  
  @keyframes loading {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }
`;

const NoJobsMessage = styled.div`
  text-align: center;
  padding: 3rem 0;
  color: var(--tertiary);
  font-size: 1.1rem;
`;

const ErrorMessage = styled.div`
  background-color: rgba(255, 99, 71, 0.1);
  border: 1px solid rgba(255, 99, 71, 0.3);
  border-radius: 4px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  color: tomato;
`;

const Pagination = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(137, 146, 176, 0.2);
`;

const PaginationButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid var(--tertiary);
  border-radius: 4px;
  background-color: transparent;
  color: var(--tertiary);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    border-color: var(--secondary);
    color: var(--secondary);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const PageInfo = styled.div`
  color: var(--tertiary);
  font-size: 0.9rem;
`;

export default JobList;
