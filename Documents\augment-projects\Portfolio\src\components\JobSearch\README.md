# Job Board Aggregator

This component provides a unified interface to search for jobs across multiple job boards, removing duplicates and allowing you to save your favorite listings.

## Features

- Search across multiple job boards (LinkedIn, Indeed, Glassdoor, etc.)
- Remove duplicate job listings
- Filter by job type, location, and more
- Save favorite jobs for later
- Real-time updates with automatic polling
- Detailed job view

## Setup

1. **Get a RapidAPI Key**:
   - Create an account on [RapidAPI](https://rapidapi.com/)
   - Subscribe to the [JSearch API](https://rapidapi.com/letscrape-6bRBa3QguO5/api/jsearch)
   - Copy your API key

2. **Update the API Key**:
   - Open `src/services/jobApi.ts`
   - Replace `YOUR_RAPIDAPI_KEY` with your actual API key

## Usage

The Job Board Aggregator is integrated into the portfolio as a section. You can:

1. **Search for Jobs**:
   - Enter keywords, job titles, or company names
   - Specify location
   - Use filters for employment type, date posted, etc.

2. **Save Jobs**:
   - Click the bookmark icon on any job card to save it
   - View all saved jobs in the "Saved Jobs" tab

3. **Apply to <PERSON>s**:
   - Click "Apply" to be redirected to the original job posting

## Technical Details

- Uses the JSearch API from RapidAPI to fetch job listings
- Implements client-side duplicate detection
- Stores saved jobs in local storage
- Uses React Context for state management
- Styled with styled-components to match the portfolio theme

## Future Enhancements

- Add more job board APIs for wider coverage
- Implement job application tracking
- Add email notifications for new matching jobs
- Integrate with resume parsing for better job matching
