import { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import SectionTitle from '../components/SectionTitle';
import FadeInSection from '../components/FadeInSection';

const Experience = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [autoRotate, setAutoRotate] = useState(true);
  const rotationRef = useRef<NodeJS.Timeout | null>(null);

  // Auto-rotate through work experiences
  useEffect(() => {
    if (!autoRotate) return;

    // Clear any existing timeout
    if (rotationRef.current) {
      clearTimeout(rotationRef.current);
    }

    // Set a longer timeout for work experience to give users time to read
    rotationRef.current = setTimeout(() => {
      // Move to next experience
      setActiveTab((prevTab) => (prevTab + 1) % experiences.length);
    }, 8000); // Rotate every 8 seconds (slower for work experience)

    return () => {
      if (rotationRef.current) {
        clearTimeout(rotationRef.current);
      }
    };
  }, [activeTab, autoRotate]);

  const experiences = [
    {
      company: 'AlgOPro Solutions',
      position: 'Senior Technical Support Engineer',
      period: 'Jan 2021 - Present',
      responsibilities: [
        'Engineered and maintained high-availability AWS/GCP infrastructure supporting HFT, consistently achieving perfect uptime via performance tuning, optimization, and observability (Datadog, Prometheus, Loki, Grafana, etc).',
        'Led mission-critical incident response & Tier 3+ escalations, performing rigorous RCA across distributed systems to rapidly restore service and implement robust preventative solutions, minimizing recurrence.',
        'Pioneered and implemented RAG/GenAI models using Python (LangChain, TensorFlow, etc) for automated error detection & context-aware troubleshooting, reducing MTTR for key incident types.',
        'Authored extensive Python automation for CI/CD validation, infra health checks, intelligent log parsing (Splunk/Datadog), monitoring, and automated failover, drastically increasing operational efficiency and reliability.',
        'Collaborated extensively with SRE, Development, and Quant teams to diagnose systemic issues, drive critical bug fixes, champion reliability features, and ensure stable production deployments.',
        'Mentored junior engineers in advanced Linux/Cloud diagnostics, Python, SQL, Bash scripting & automation, and incident management.'
      ]
    },
    {
      company: 'Pho House & Hong Thanh',
      position: 'Technical Lead & Co-Owner',
      period: 'May 2014 - Apr 2020',
      responsibilities: [
        'Directed end-to-end IT strategy, operations, security, and cloud services (AWS/GCP) across two locations ($1M+ combined revenue), managing infrastructure, vendors, and staff technical training.',
        'Spearheaded development & deployment of a custom iOS POS system with cloud inventory based of off touch bistro, which helped improved order efficiency.',
        'Executed AWS-to-GCP migration, enhancing scalability, security, and implementing Python-automated backups/reporting.',
        'Developed Python analytics tools delivering business intelligence that informed strategy and contributed to a minimum 10% YoY revenue growth.'
      ]
    },
    {
      company: 'The Pennsylvania State University',
      position: 'Tier 3 Support Specialist',
      period: 'Sep 2012 - Mar 2015',
      responsibilities: [
        'Served as final technical escalation point for central IT, resolving complex issues across university systems (Linux/Windows servers, network infrastructure).',
        'Enhanced system stability via implementation of proactive infrastructure monitoring protocols, reducing service disruptions.',
        'Developed and delivered impactful technical training, improving Tier 1/2 diagnostic skills and standardizing troubleshooting methodologies.',
        'Engineered Python automation for processing support emails into ticketing system, significantly improving workflow efficiency.'
      ]
    },
    {
      company: 'The Pennsylvania State University',
      position: 'Tier 1 & 2 Support Desk Specialist',
      period: 'Aug 2010 - Jul 2012',
      responsibilities: [
        'Provided highly-rated Tier 1/2 multi-platform support (Windows, Mac, iOS, Android), achieving top 5/5 CSAT scores through effective communication and rapid resolution.',
        'Managed hardware/software troubleshooting and meticulous documentation within SAP ticketing system.'
      ]
    }
  ];

  return (
    <section id="experience" className="py-20 bg-primary">
      <div className="section-container">
        <SectionTitle number="03" title="Work Experience" />

        <div className="flex flex-col md:flex-row gap-8 w-full">
          {/* Tab buttons */}
          <FadeInSection className="md:w-1/4" delay={0.2}>
            <div className="flex md:flex-col overflow-x-auto md:overflow-x-visible mb-6 md:mb-0">
              {experiences.map((exp, index) => (
                <button
                  key={index}
                  onClick={() => {
                    setAutoRotate(false); // Stop auto-rotation when user clicks
                    setActiveTab(index);
                  }}
                  className={`px-4 py-3 text-left whitespace-nowrap md:whitespace-normal border-b-2 md:border-b-0 md:border-l-2 transition-all duration-300 ${
                    activeTab === index
                      ? 'text-secondary border-secondary bg-secondary/5 md:bg-secondary/10'
                      : 'text-tertiary border-tertiary/30 hover:text-light hover:bg-dark/50'
                  }`}
                >
                  {exp.company}
                </button>
              ))}
            </div>
          </FadeInSection>

          {/* Tab content */}
          <FadeInSection className="md:w-3/4" delay={0.4}>
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <h3 className="text-xl font-bold text-light">
                {experiences[activeTab].position} <span className="text-secondary">@ {experiences[activeTab].company}</span>
              </h3>
              <p className="text-tertiary font-mono mb-4">{experiences[activeTab].period}</p>

              <ul className="space-y-4">
                {experiences[activeTab].responsibilities.map((item, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-secondary mr-2 mt-1.5">▹</span>
                    <span className="text-tertiary text-sm">{item}</span>
                  </li>
                ))}
              </ul>
            </motion.div>
          </FadeInSection>
        </div>
      </div>
    </section>
  );
};

export default Experience;
