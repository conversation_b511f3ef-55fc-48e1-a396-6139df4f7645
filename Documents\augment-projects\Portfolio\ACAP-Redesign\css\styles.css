/* 
ACAP Website - Ultra Modern Redesign
Modern Color Palette:
- Primary: #2563EB (Modern Blue)
- Secondary: #7C3AED (Purple Accent)
- Success: #10B981 (Medical Green)
- Background: #F8FAFC (Clean White)
- Dark: #0F172A (Deep Navy)
- Glass: rgba(255, 255, 255, 0.1)
*/

/* CSS Custom Properties */
:root {
    --primary-color: #2563EB;
    --primary-dark: #1D4ED8;
    --secondary-color: #7C3AED;
    --success-color: #10B981;
    --warning-color: #F59E0B;
    --danger-color: #EF4444;
    --dark-color: #0F172A;
    --light-color: #F8FAFC;
    --white: #FFFFFF;
    --gray-50: #F9FAFB;
    --gray-100: #F3F4F6;
    --gray-200: #E5E7EB;
    --gray-300: #D1D5DB;
    --gray-400: #9CA3AF;
    --gray-500: #6B7280;
    --gray-600: #4B5563;
    --gray-700: #374151;
    --gray-800: #1F2937;
    --gray-900: #111827;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #2563EB 0%, #7C3AED 100%);
    --gradient-secondary: linear-gradient(135deg, #10B981 0%, #059669 100%);
    --gradient-hero: linear-gradient(135deg, #0F172A 0%, #1E293B 50%, #334155 100%);
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    
    /* Glass Effect */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;
}

/* Base Styles & Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--white);
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

h1 { font-size: clamp(2.5rem, 5vw, 4rem); }
h2 { font-size: clamp(2rem, 4vw, 3rem); }
h3 { font-size: clamp(1.5rem, 3vw, 2rem); }
h4 { font-size: clamp(1.25rem, 2.5vw, 1.5rem); }

p {
    margin-bottom: 1rem;
    color: var(--gray-600);
    line-height: 1.7;
}

.lead {
    font-size: 1.25rem;
    font-weight: 400;
    color: var(--gray-700);
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: var(--transition-normal);
}

a:hover {
    color: var(--primary-dark);
}

/* Container */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.125rem;
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

.section-badge {
    display: inline-block;
    padding: 0.5rem 1rem;
    background: var(--gradient-primary);
    color: white;
    border-radius: var(--radius-full);
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 1rem;
}

/* Modern Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-lg);
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    font-size: 0.875rem;
    text-align: center;
    transition: var(--transition-normal);
    border: none;
    cursor: pointer;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-slow);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    color: white;
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.btn-glass {
    background: var(--glass-bg);
    color: white;
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(10px);
}

.btn-glass:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    transform: translateY(-2px);
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1rem;
}

.btn-link {
    color: var(--primary-color);
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition-normal);
}

.btn-link:hover {
    color: var(--primary-dark);
    transform: translateX(4px);
}

/* Modern Header */
.modern-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--gray-200);
    transition: var(--transition-normal);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
}

.logo-modern {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-xl);
    font-weight: 800;
    font-size: 1.25rem;
    box-shadow: var(--shadow-lg);
}

.logo-text h1 {
    font-size: 1.5rem;
    margin-bottom: 0.25rem;
    color: var(--dark-color);
}

.chinese-text {
    font-size: 1rem;
    color: var(--gray-500);
    font-weight: 400;
    margin: 0;
}

.main-navigation {
    display: none;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: var(--gray-700);
    font-weight: 500;
    padding: 0.5rem 0;
    position: relative;
    transition: var(--transition-normal);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: var(--transition-normal);
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.mobile-menu-toggle {
    display: flex;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
}

.mobile-menu-toggle span {
    width: 24px;
    height: 2px;
    background: var(--gray-700);
    transition: var(--transition-normal);
}

@media (min-width: 768px) {
    .main-navigation {
        display: block;
    }
    
    .mobile-menu-toggle {
        display: none;
    }
}

/* Hero Section */
.hero-modern {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    color: white;
    overflow: hidden;
    background: var(--gradient-hero);
}

.hero-background-animated {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-hero);
    z-index: -2;
}

.hero-background-animated::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(1deg); }
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.hero-particles::before,
.hero-particles::after {
    content: '';
    position: absolute;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(37, 99, 235, 0.3) 0%, transparent 70%);
    animation: particle-float 15s ease-in-out infinite;
}

.hero-particles::before {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.hero-particles::after {
    bottom: 20%;
    right: 10%;
    animation-delay: 7s;
}

@keyframes particle-float {
    0%, 100% { transform: translate(0, 0) scale(1); }
    33% { transform: translate(30px, -30px) scale(1.1); }
    66% { transform: translate(-20px, 20px) scale(0.9); }
}

.hero-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    padding: 8rem 0 4rem;
}

.hero-badge {
    display: inline-block;
    padding: 0.5rem 1rem;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-full);
    backdrop-filter: blur(10px);
    margin-bottom: 2rem;
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

.hero-title {
    font-size: clamp(3rem, 6vw, 5rem);
    font-weight: 700;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: fadeInUp 0.8s ease-out 0.4s both;
}

.hero-subtitle {
    font-size: 1.5rem;
    font-weight: 500;
    margin-bottom: 1rem;
    color: var(--gray-300);
    animation: fadeInUp 0.8s ease-out 0.6s both;
}

.hero-description {
    font-size: 1.125rem;
    color: var(--gray-400);
    margin-bottom: 3rem;
    animation: fadeInUp 0.8s ease-out 0.8s both;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 4rem;
    animation: fadeInUp 0.8s ease-out 1s both;
}

.hero-stats {
    display: flex;
    gap: 3rem;
    justify-content: center;
    flex-wrap: wrap;
    animation: fadeInUp 0.8s ease-out 1.2s both;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: white;
    display: block;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--gray-400);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Featured Events */
.featured-events {
    padding: 6rem 0;
    background: var(--gray-50);
}

.events-carousel {
    position: relative;
    margin-bottom: 3rem;
}

.event-slide {
    display: none;
    background: white;
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    transition: var(--transition-normal);
}

.event-slide.active {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0;
}

.event-image {
    position: relative;
    overflow: hidden;
}

.event-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    transition: var(--transition-slow);
}

.event-slide:hover .event-image img {
    transform: scale(1.05);
}

.event-overlay {
    position: absolute;
    top: 1rem;
    left: 1rem;
}

.event-date {
    background: var(--gradient-primary);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 0.875rem;
}

.event-content {
    padding: 3rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.event-content h3 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.event-content p {
    font-size: 1.125rem;
    margin-bottom: 2rem;
    color: var(--gray-600);
}

.carousel-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 3rem;
}

.carousel-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: white;
    border: 2px solid var(--gray-200);
    color: var(--gray-600);
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
}

.carousel-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: scale(1.1);
}

.carousel-dots {
    display: flex;
    gap: 0.5rem;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--gray-300);
    cursor: pointer;
    transition: var(--transition-normal);
}

.dot.active {
    background: var(--primary-color);
    transform: scale(1.2);
}

.newsletter-cta {
    background: white;
    border-radius: var(--radius-2xl);
    padding: 2rem;
    box-shadow: var(--shadow-lg);
}

.newsletter-content {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    flex-wrap: wrap;
    justify-content: space-between;
}

.newsletter-content i {
    font-size: 2rem;
    color: var(--primary-color);
}

.newsletter-content h4 {
    margin: 0;
    color: var(--dark-color);
}

.newsletter-content p {
    margin: 0;
    color: var(--gray-600);
}

/* About Section */
.about-modern {
    padding: 6rem 0;
    background: white;
}

.about-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 4rem;
    align-items: center;
}

.about-features {
    margin-top: 2rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.feature-item i {
    color: var(--success-color);
    font-size: 1.25rem;
}

.about-visual {
    display: flex;
    justify-content: center;
}

.about-card {
    background: var(--gradient-primary);
    color: white;
    padding: 3rem;
    border-radius: var(--radius-2xl);
    text-align: center;
    box-shadow: var(--shadow-2xl);
    transform: rotate(-5deg);
    transition: var(--transition-normal);
}

.about-card:hover {
    transform: rotate(0deg) scale(1.05);
}

.about-card .card-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
}

.about-card h4 {
    color: white;
    margin-bottom: 1rem;
}

.about-card p {
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
}

@media (min-width: 768px) {
    .about-grid {
        grid-template-columns: 1fr 1fr;
    }
    
    .event-slide.active {
        display: grid;
    }
}

@media (max-width: 767px) {
    .event-slide.active {
        display: block;
    }
    
    .event-image img {
        height: 250px;
    }
    
    .event-content {
        padding: 2rem;
    }
    
    .newsletter-content {
        text-align: center;
        justify-content: center;
    }
}

/* Mission Section */
.mission-modern {
    padding: 6rem 0;
    background: var(--gray-50);
}

.mission-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.mission-card {
    background: white;
    padding: 3rem 2rem;
    border-radius: var(--radius-2xl);
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: var(--transition-normal);
    border: 1px solid var(--gray-200);
}

.mission-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: var(--transition-normal);
}

.mission-card:hover::before {
    transform: scaleX(1);
}

.mission-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-2xl);
}

.mission-card .card-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    font-size: 2rem;
    transition: var(--transition-normal);
}

.mission-card:hover .card-icon {
    transform: scale(1.1) rotate(5deg);
}

.mission-card h3 {
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.mission-card p {
    color: var(--gray-600);
    line-height: 1.6;
}

.card-hover-effect {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0;
    transition: var(--transition-normal);
    z-index: -1;
}

/* Programs Preview */
.programs-preview {
    padding: 6rem 0;
    background: white;
}

.programs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.program-card {
    background: white;
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
    position: relative;
}

.program-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-2xl);
}

.program-image {
    position: relative;
    overflow: hidden;
}

.program-image img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: var(--transition-slow);
}

.program-card:hover .program-image img {
    transform: scale(1.1);
}

.program-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--primary-color);
    opacity: 0;
    transition: var(--transition-normal);
}

.program-card:hover .program-overlay {
    opacity: 1;
}

.program-content {
    padding: 2rem;
}

.program-content h3 {
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.program-content p {
    color: var(--gray-600);
    margin-bottom: 1.5rem;
}

.programs-cta {
    text-align: center;
}

/* COVID Response */
.covid-response {
    padding: 6rem 0;
    background: var(--gradient-secondary);
    color: white;
    text-align: center;
}

.covid-content {
    max-width: 800px;
    margin: 0 auto;
}

.covid-content .section-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.covid-content h2 {
    color: white;
    margin-bottom: 2rem;
}

.covid-content .lead {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.25rem;
    margin-bottom: 2rem;
}

.covid-content p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.125rem;
}

/* Testimonials */
.testimonials-modern {
    padding: 6rem 0;
    background: var(--gray-50);
}

.testimonial-slider {
    max-width: 800px;
    margin: 0 auto 3rem;
}

.testimonial-card {
    display: none;
    background: white;
    border-radius: var(--radius-2xl);
    padding: 3rem;
    box-shadow: var(--shadow-xl);
    text-align: center;
    position: relative;
}

.testimonial-card.active {
    display: block;
}

.quote-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    font-size: 1.5rem;
}

.testimonial-content p {
    font-size: 1.25rem;
    font-style: italic;
    color: var(--gray-700);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.testimonial-author {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.author-info h4 {
    color: var(--primary-color);
    margin: 0;
}

.author-info span {
    color: var(--gray-500);
    font-size: 0.875rem;
}

.testimonial-controls {
    text-align: center;
}

.testimonial-dots {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
}

/* CTA Section */
.cta-modern {
    padding: 6rem 0;
    background: var(--dark-color);
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.cta-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    opacity: 0.5;
    z-index: -1;
}

.cta-content {
    position: relative;
    z-index: 1;
}

.cta-content h2 {
    color: white;
    margin-bottom: 1.5rem;
}

.cta-content p {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Modern Footer */
.footer-modern {
    background: var(--dark-color);
    color: white;
    padding: 4rem 0 2rem;
    position: relative;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-main {
    text-align: center;
}

.footer-brand {
    margin-bottom: 2rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.footer-logo .logo-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-xl);
    font-weight: 800;
    font-size: 1.25rem;
}

.brand-text h3 {
    color: white;
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
}

.brand-text p {
    color: var(--gray-400);
    margin: 0;
    font-size: 1rem;
}

.footer-brand p {
    color: var(--gray-400);
    max-width: 400px;
    margin: 0 auto 2rem;
    line-height: 1.6;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.social-link {
    width: 50px;
    height: 50px;
    background: var(--gray-800);
    color: var(--gray-400);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-normal);
    font-size: 1.25rem;
}

.social-link:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-3px);
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.footer-column h4 {
    color: white;
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
}

.footer-column ul {
    list-style: none;
}

.footer-column ul li {
    margin-bottom: 0.75rem;
}

.footer-column ul li a {
    color: var(--gray-400);
    transition: var(--transition-normal);
    display: block;
    padding: 0.25rem 0;
}

.footer-column ul li a:hover {
    color: white;
    transform: translateX(5px);
}

.contact-info {
    space-y: 1rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
    color: var(--gray-400);
}

.contact-item i {
    color: var(--primary-color);
    font-size: 1.25rem;
    margin-top: 0.25rem;
    flex-shrink: 0;
}

.contact-item div {
    display: flex;
    flex-direction: column;
}

.contact-item span {
    line-height: 1.4;
}

.contact-item a {
    color: var(--gray-400);
    transition: var(--transition-normal);
}

.contact-item a:hover {
    color: white;
}

.footer-bottom {
    border-top: 1px solid var(--gray-800);
    padding-top: 2rem;
}

.footer-bottom-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    text-align: center;
}

.footer-bottom p {
    color: var(--gray-500);
    margin: 0;
}

.legal-links {
    display: flex;
    gap: 2rem;
}

.legal-links a {
    color: var(--gray-500);
    font-size: 0.875rem;
    transition: var(--transition-normal);
}

.legal-links a:hover {
    color: white;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    box-shadow: var(--shadow-xl);
    transition: var(--transition-normal);
    opacity: 0;
    visibility: hidden;
    z-index: 1000;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-2xl);
}

/* Mobile Menu */
.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Responsive Design */
@media (min-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr 2fr;
    }
    
    .footer-main {
        text-align: left;
    }
    
    .footer-logo {
        justify-content: flex-start;
    }
    
    .footer-brand p {
        margin-left: 0;
        margin-right: 0;
    }
    
    .social-links {
        justify-content: flex-start;
    }
    
    .footer-bottom-content {
        flex-direction: row;
        justify-content: space-between;
        text-align: left;
    }
}

@media (max-width: 767px) {
    .hero-content {
        padding: 6rem 0 3rem;
    }
    
    .hero-stats {
        gap: 2rem;
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-large {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
    
    .programs-grid {
        grid-template-columns: 1fr;
    }
    
    .mission-grid {
        grid-template-columns: 1fr;
    }
    
    .about-grid {
        grid-template-columns: 1fr;
        gap: 3rem;
    }
    
    .about-card {
        transform: none;
        margin: 0 auto;
        max-width: 300px;
    }
    
    .about-card:hover {
        transform: scale(1.05);
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 2rem; }
.mb-3 { margin-bottom: 3rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 1rem; }
.mt-2 { margin-top: 2rem; }
.mt-3 { margin-top: 3rem; }

.hidden { display: none; }
.visible { display: block; }

/* Print Styles */
@media print {
    .modern-header,
    .back-to-top,
    .mobile-menu-overlay {
        display: none;
    }
    
    .hero-modern {
        min-height: auto;
        padding: 2rem 0;
    }
    
    * {
        box-shadow: none !important;
    }
}
