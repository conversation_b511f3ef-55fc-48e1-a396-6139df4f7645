import { motion } from 'framer-motion';
import { FiGithub, FiLinkedin, FiMail } from 'react-icons/fi';
import styled from 'styled-components';

const IconContainer = styled.div`
  position: fixed;
  top: 0.25rem;
  left: 1rem;
  display: flex;
  flex-direction: row;
  padding: 0.25rem 0;
  z-index: 100;
  background-color: transparent;
  pointer-events: none; /* Allow clicks to pass through the container */
`;

const IconWrapper = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
`;

const IconLink = styled.a`
  color: var(--tertiary);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: auto; /* Re-enable pointer events for links */

  &:hover {
    color: var(--secondary);
    transform: translateY(-2px);
  }
`;

const TopSocialIcons = () => {
  return (
    <IconContainer>
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
      >
        <IconWrapper>
          <IconLink
            href="https://github.com/MikeDominic92"
            target="_blank"
            rel="noopener noreferrer"
            aria-label="GitHub"
          >
            <FiGithub size={18} />
          </IconLink>
          <IconLink
            href="https://www.linkedin.com/in/mdhlee/"
            target="_blank"
            rel="noopener noreferrer"
            aria-label="LinkedIn"
          >
            <FiLinkedin size={18} />
          </IconLink>
          <IconLink
            href="mailto:<EMAIL>"
            aria-label="Email"
          >
            <FiMail size={18} />
          </IconLink>
        </IconWrapper>
      </motion.div>
    </IconContainer>
  );
};

export default TopSocialIcons;
