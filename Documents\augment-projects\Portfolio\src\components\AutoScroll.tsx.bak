import { useEffect, useState } from 'react';
import { scroller } from 'react-scroll';

// Define the sections in order
const sections = [
  'hero',
  'about',
  'skills',
  'experience',
  'projects',
  'certifications',
  'contact'
];

// Time to stay on each section before scrolling (in milliseconds)
const SECTION_DISPLAY_TIME = 8000; // 8 seconds

const AutoScroll = () => {
  const [currentSectionIndex, setCurrentSectionIndex] = useState(0);
  const [isUserScrolling, setIsUserScrolling] = useState(false);

  // Function to scroll to the next section
  const scrollToNextSection = () => {
    if (isUserScrolling) return;

    const nextIndex = (currentSectionIndex + 1) % sections.length;
    setCurrentSectionIndex(nextIndex);

    scroller.scrollTo(sections[nextIndex], {
      duration: 1000,
      smooth: true,
      offset: -70 // Same offset as in the navbar
    });
  };

  // Reset the auto-scroll timer when user manually scrolls
  const handleUserScroll = () => {
    setIsUserScrolling(true);

    // Reset the auto-scroll after user stops scrolling for 5 seconds
    setTimeout(() => {
      setIsUserScrolling(false);
    }, 5000);
  };

  useEffect(() => {
    // Set up the auto-scroll timer
    const timer = setTimeout(() => {
      if (!isUserScrolling) {
        scrollToNextSection();
      }
    }, SECTION_DISPLAY_TIME);

    // Add scroll event listener
    window.addEventListener('scroll', handleUserScroll);

    // Clean up
    return () => {
      clearTimeout(timer);
      window.removeEventListener('scroll', handleUserScroll);
    };
  }, [currentSectionIndex, isUserScrolling]);

  return null; // This component doesn't render anything
};

export default AutoScroll;
