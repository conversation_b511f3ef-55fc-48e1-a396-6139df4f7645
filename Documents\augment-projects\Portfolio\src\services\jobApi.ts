import { Job, JobSearchParams, JobSearchResponse } from '../types/Job';

// This would normally be in an environment variable
const RAPID_API_KEY = 'YOUR_RAPIDAPI_KEY';
const RAPID_API_HOST = 'jsearch.p.rapidapi.com';

/**
 * Fetch jobs from the JSearch API
 */
export const searchJobs = async (params: JobSearchParams): Promise<JobSearchResponse> => {
  try {
    const queryParams = new URLSearchParams({
      query: params.query,
      page: params.page?.toString() || '1',
      num_pages: params.num_pages?.toString() || '1',
      ...(params.employment_types && { employment_types: params.employment_types.join(',') }),
      ...(params.job_requirements && { job_requirements: params.job_requirements.join(',') }),
      ...(params.date_posted && { date_posted: params.date_posted }),
      ...(params.remote_jobs_only !== undefined && { remote_jobs_only: params.remote_jobs_only.toString() }),
      ...(params.country && { country: params.country }),
    });

    const response = await fetch(`https://jsearch.p.rapidapi.com/search?${queryParams}`, {
      method: 'GET',
      headers: {
        'X-RapidAPI-Key': RAPID_API_KEY,
        'X-RapidAPI-Host': RAPID_API_HOST,
      },
    });

    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}`);
    }

    const data = await response.json();
    return {
      status: data.status,
      data: data.data.map((job: any) => ({
        ...job,
        source: 'JSearch API',
      })),
      num_results: data.num_results,
    };
  } catch (error) {
    console.error('Error fetching jobs:', error);
    return {
      status: 'error',
      data: [],
      num_results: 0,
    };
  }
};

/**
 * Remove duplicate job listings based on job title and employer
 */
export const removeDuplicateJobs = (jobs: Job[]): Job[] => {
  const uniqueJobs = new Map<string, Job>();
  
  jobs.forEach(job => {
    // Create a unique key based on job title and employer
    const key = `${job.job_title.toLowerCase()}-${job.employer_name.toLowerCase()}`;
    
    // Only add if we haven't seen this job before, or if this one is newer
    if (!uniqueJobs.has(key) || 
        new Date(job.job_posted_at_datetime_utc) > 
        new Date(uniqueJobs.get(key)!.job_posted_at_datetime_utc)) {
      uniqueJobs.set(key, job);
    }
  });
  
  return Array.from(uniqueJobs.values());
};

/**
 * Mock function to simulate fetching jobs from multiple sources
 * In a real implementation, you would integrate with multiple APIs
 */
export const fetchJobsFromMultipleSources = async (
  query: string, 
  page: number = 1
): Promise<Job[]> => {
  // For now, we'll just use JSearch API
  const response = await searchJobs({ query, page });
  
  // In a real implementation, you would fetch from multiple sources
  // and combine the results
  
  // Remove duplicates
  return removeDuplicateJobs(response.data);
};
