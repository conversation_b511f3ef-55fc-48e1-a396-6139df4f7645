import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { FiX, FiBookmark, FiExternalLink, FiMapPin, FiClock, FiDollarSign, FiBriefcase } from 'react-icons/fi';
import { Job } from '../../types/Job';
import { saveJob, unsaveJob } from '../../services/jobStorage';
import { useJobContext } from '../../context/JobContext';

interface JobDetailProps {
  job: Job;
}

const JobDetail: React.FC<JobDetailProps> = ({ job }) => {
  const { refreshJobs, setSelectedJob } = useJobContext();

  const handleSaveToggle = () => {
    if (job.saved) {
      unsaveJob(job.job_id);
    } else {
      saveJob(job);
    }
    refreshJobs();
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <DetailContainer>
      <DetailHeader>
        <CloseButton onClick={() => setSelectedJob(null)}>
          <FiX />
        </CloseButton>
        <CompanyLogo>
          {job.employer_logo ? (
            <img src={job.employer_logo} alt={`${job.employer_name} logo`} />
          ) : (
            <div className="placeholder">{job.employer_name.charAt(0)}</div>
          )}
        </CompanyLogo>
        <HeaderInfo>
          <JobTitle>{job.job_title}</JobTitle>
          <CompanyName>{job.employer_name}</CompanyName>
        </HeaderInfo>
      </DetailHeader>

      <DetailBody>
        <JobDetails>
          {job.job_employment_type && (
            <DetailItem>
              <FiBriefcase />
              <span>{job.job_employment_type.replace('_', ' ')}</span>
            </DetailItem>
          )}
          {(job.job_city || job.job_country) && (
            <DetailItem>
              <FiMapPin />
              <span>
                {job.job_city ? `${job.job_city}, ` : ''}
                {job.job_state ? `${job.job_state}, ` : ''}
                {job.job_country}
                {job.job_is_remote && ' (Remote)'}
              </span>
            </DetailItem>
          )}
          {job.job_salary && (
            <DetailItem>
              <FiDollarSign />
              <span>{job.job_salary}</span>
            </DetailItem>
          )}
          <DetailItem>
            <FiClock />
            <span>Posted on {formatDate(job.job_posted_at_datetime_utc)}</span>
          </DetailItem>
        </JobDetails>

        <ActionButtons>
          <SaveButton onClick={handleSaveToggle} saved={job.saved}>
            <FiBookmark />
            {job.saved ? 'Saved' : 'Save Job'}
          </SaveButton>
          <ApplyButton href={job.job_apply_link} target="_blank" rel="noopener noreferrer">
            Apply Now <FiExternalLink />
          </ApplyButton>
        </ActionButtons>

        <JobDescription dangerouslySetInnerHTML={{ __html: formatDescription(job.job_description) }} />
      </DetailBody>

      <DetailFooter>
        <SourceTag>{job.source}</SourceTag>
        <ApplyButton href={job.job_apply_link} target="_blank" rel="noopener noreferrer">
          Apply Now <FiExternalLink />
        </ApplyButton>
      </DetailFooter>
    </DetailContainer>
  );
};

// Helper function to format job description with proper HTML
const formatDescription = (description: string): string => {
  // Replace newlines with <br> tags
  let formatted = description.replace(/\n/g, '<br>');
  
  // Add basic formatting for lists if they exist
  formatted = formatted.replace(/•\s(.*?)(?=<br>|$)/g, '<li>$1</li>');
  if (formatted.includes('<li>')) {
    formatted = formatted.replace(/<li>(.*?)<\/li>/g, '<ul><li>$1</li></ul>');
    formatted = formatted.replace(/<\/ul><ul>/g, '');
  }
  
  return formatted;
};

const DetailContainer = styled(motion.div)`
  background-color: var(--primary);
  border: 1px solid var(--tertiary);
  border-radius: 8px;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
`;

const DetailHeader = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(137, 146, 176, 0.2);
`;

const CloseButton = styled.button`
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: var(--tertiary);
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    color: var(--secondary);
    transform: scale(1.1);
  }
`;

const CompanyLogo = styled.div`
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 1rem;
  background-color: var(--tertiary);
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .placeholder {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--light);
  }
`;

const HeaderInfo = styled.div`
  flex: 1;
`;

const JobTitle = styled.h2`
  margin: 0;
  font-size: 1.5rem;
  color: var(--light);
`;

const CompanyName = styled.p`
  margin: 0.25rem 0 0;
  font-size: 1rem;
  color: var(--tertiary);
`;

const DetailBody = styled.div`
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
`;

const JobDetails = styled.div`
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 1.5rem;
  gap: 1rem;
`;

const DetailItem = styled.div`
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  color: var(--tertiary);
  margin-right: 1rem;

  svg {
    margin-right: 0.5rem;
  }
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
`;

interface SaveButtonProps {
  saved?: boolean;
}

const SaveButton = styled.button<SaveButtonProps>`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: 1px solid ${props => (props.saved ? 'var(--secondary)' : 'var(--tertiary)')};
  border-radius: 4px;
  background-color: ${props => (props.saved ? 'rgba(100, 255, 218, 0.1)' : 'transparent')};
  color: ${props => (props.saved ? 'var(--secondary)' : 'var(--tertiary)')};
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: var(--secondary);
    color: var(--secondary);
  }
`;

const ApplyButton = styled.a`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  background-color: var(--secondary);
  color: var(--primary);
  font-size: 1rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(100, 255, 218, 0.8);
  }
`;

const JobDescription = styled.div`
  color: var(--light);
  line-height: 1.6;
  font-size: 0.95rem;

  ul {
    padding-left: 1.5rem;
    margin: 1rem 0;
  }

  li {
    margin-bottom: 0.5rem;
  }
`;

const DetailFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-top: 1px solid rgba(137, 146, 176, 0.2);
`;

const SourceTag = styled.span`
  font-size: 0.8rem;
  color: var(--tertiary);
  background-color: rgba(100, 255, 218, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
`;

export default JobDetail;
