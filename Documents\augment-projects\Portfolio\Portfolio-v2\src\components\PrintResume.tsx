import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiPrinter, FiX } from 'react-icons/fi';

const PrintResume = () => {
  const [isOpen, setIsOpen] = useState(false);

  const handlePrint = () => {
    window.open('/resume.pdf', '_blank');
  };

  return (
    <>
      <motion.button
        className="fixed top-0.5 right-6 z-[100] w-8 h-8 rounded-full bg-secondary/10 border border-secondary/30 flex items-center justify-center text-secondary hover:bg-secondary/20 transition-colors"
        onClick={() => setIsOpen(true)}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        aria-label="Print resume"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3, delay: 1.2 }}
      >
        <FiPrinter size={18} />
      </motion.button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="fixed inset-0 bg-dark/80 backdrop-blur-sm z-[200] flex items-center justify-center p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <motion.div
              className="bg-primary border border-secondary/20 rounded-lg shadow-xl max-w-md w-full p-6"
              initial={{ scale: 0.9, y: 20 }}
              animate={{ scale: 1, y: 0 }}
              exit={{ scale: 0.9, y: 20 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-xl font-bold text-light">Resume Options</h3>
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-tertiary hover:text-secondary transition-colors"
                  aria-label="Close"
                >
                  <FiX size={20} />
                </button>
              </div>

              <p className="text-tertiary mb-6">
                Choose how you'd like to view or download my resume:
              </p>

              <div className="space-y-4">
                <button
                  onClick={handlePrint}
                  className="w-full py-3 px-4 bg-secondary/10 hover:bg-secondary/20 text-secondary border border-secondary/30 rounded-md transition-colors flex items-center justify-center gap-2"
                >
                  <FiPrinter size={18} />
                  <span>View PDF Resume</span>
                </button>

                <a
                  href="/resume.pdf"
                  download
                  className="w-full py-3 px-4 bg-secondary text-primary hover:bg-secondary/90 rounded-md transition-colors flex items-center justify-center"
                >
                  Download Resume
                </a>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default PrintResume;
