import { ReactNode } from 'react';
import { motion } from 'framer-motion';
import styled from 'styled-components';

interface FadeInSectionProps {
  children: ReactNode;
  delay?: number;
  direction?: 'up' | 'down' | 'left' | 'right' | 'none';
  className?: string;
  viewportMargin?: string;
}

const AnimatedSection = styled(motion.div)`
  /* Base styles can be added here if needed */
`;

const FadeInSection = ({
  children,
  delay = 0,
  direction = 'up',
  className = '',
  viewportMargin = "-100px"
}: FadeInSectionProps) => {

  const getInitialPosition = () => {
    switch (direction) {
      case 'up': return { opacity: 0, y: 50 };
      case 'down': return { opacity: 0, y: -50 };
      case 'left': return { opacity: 0, x: 50 };
      case 'right': return { opacity: 0, x: -50 };
      case 'none': return { opacity: 0 };
      default: return { opacity: 0, y: 50 };
    }
  };

  const getFinalPosition = () => {
    switch (direction) {
      case 'up':
      case 'down':
        return { opacity: 1, y: 0 };
      case 'left':
      case 'right':
        return { opacity: 1, x: 0 };
      case 'none':
        return { opacity: 1 };
      default:
        return { opacity: 1, y: 0 };
    }
  };

  return (
    <AnimatedSection
      className={className}
      initial={getInitialPosition()}
      whileInView={getFinalPosition()}
      viewport={{ once: true, margin: viewportMargin }}
      transition={{ duration: 0.7, delay: delay }}
    >
      {children}
    </AnimatedSection>
  );
};

export default FadeInSection;
