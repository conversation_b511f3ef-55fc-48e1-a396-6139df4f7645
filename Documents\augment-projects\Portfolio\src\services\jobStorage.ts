import { Job } from '../types/Job';

const SAVED_JOBS_KEY = 'savedJobs';

/**
 * Save a job to local storage
 */
export const saveJob = (job: Job): void => {
  const savedJobs = getSavedJobs();
  
  // Check if job is already saved
  if (!savedJobs.some(savedJob => savedJob.job_id === job.job_id)) {
    savedJobs.push({
      ...job,
      saved: true
    });
    localStorage.setItem(SAVED_JOBS_KEY, JSON.stringify(savedJobs));
  }
};

/**
 * Remove a job from saved jobs
 */
export const unsaveJob = (jobId: string): void => {
  const savedJobs = getSavedJobs();
  const updatedJobs = savedJobs.filter(job => job.job_id !== jobId);
  localStorage.setItem(SAVED_JOBS_KEY, JSON.stringify(updatedJobs));
};

/**
 * Get all saved jobs from local storage
 */
export const getSavedJobs = (): Job[] => {
  const savedJobsJson = localStorage.getItem(SAVED_JOBS_KEY);
  return savedJobsJson ? JSON.parse(savedJobsJson) : [];
};

/**
 * Check if a job is saved
 */
export const isJobSaved = (jobId: string): boolean => {
  const savedJobs = getSavedJobs();
  return savedJobs.some(job => job.job_id === jobId);
};

/**
 * Mark saved jobs in a list of jobs
 */
export const markSavedJobs = (jobs: Job[]): Job[] => {
  const savedJobs = getSavedJobs();
  const savedJobIds = new Set(savedJobs.map(job => job.job_id));
  
  return jobs.map(job => ({
    ...job,
    saved: savedJobIds.has(job.job_id)
  }));
};
