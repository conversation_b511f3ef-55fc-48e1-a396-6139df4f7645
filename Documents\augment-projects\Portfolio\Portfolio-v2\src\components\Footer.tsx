// No icons needed
import styled from 'styled-components';

const FooterContainer = styled.footer`
  background-color: var(--primary);
  padding: 2rem 0;
  border-top: 1px solid rgba(136, 146, 176, 0.1);
`;

const FooterInner = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;

  @media (min-width: 640px) {
    padding: 0 1.5rem;
  }

  @media (min-width: 1024px) {
    padding: 0 2rem;
  }
`;

const FooterContent = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
`;

const FooterText = styled.p`
  color: var(--tertiary);
  font-size: 0.875rem;
  text-align: center;
`;

// Social links removed

const Footer = () => {
  return (
    <FooterContainer>
      <FooterInner>
        <FooterContent>
          <FooterText>
            &copy; {new Date().getFullYear()} Michael <PERSON>ang. All rights reserved.
          </FooterText>
        </FooterContent>
      </FooterInner>
    </FooterContainer>
  );
};

export default Footer;
