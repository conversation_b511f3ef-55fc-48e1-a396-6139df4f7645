import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

type Badge = {
  name: string;
  icon: string;
  color: string;
  hoverText: string;
};

type TechBadgesProps = {
  className?: string;
};

const TechBadges = ({ className = '' }: TechBadgesProps) => {
  const [activeIndex, setActiveIndex] = useState<number>(0);
  const [isAutoScrolling, setIsAutoScrolling] = useState<boolean>(true);

  // Auto-cycle through tech badges
  useEffect(() => {
    if (!isAutoScrolling) return;

    const interval = setInterval(() => {
      setActiveIndex((prevIndex) => (prevIndex + 1) % badges.length);
    }, 4000); // Change every 4 seconds

    return () => clearInterval(interval);
  }, [isAutoScrolling]);

  // Pause auto-cycling when user interacts with badges
  const handleBadgeClick = (index: number) => {
    setActiveIndex(index);
    setIsAutoScrolling(false);

    // Resume auto-cycling after 10 seconds of inactivity
    const timeout = setTimeout(() => {
      setIsAutoScrolling(true);
    }, 10000);

    return () => clearTimeout(timeout);
  };

  const badges: Badge[] = [
    {
      name: 'Cloud',
      icon: '☁️',
      color: 'bg-blue-500 border-blue-400',
      hoverText: 'Built my career on AWS and GCP. Still amazed when things don\'t crash during demos!'
    },
    {
      name: 'Security',
      icon: '🔒',
      color: 'bg-red-500 border-red-400',
      hoverText: 'Trust issues make for good security practices. Just ask my password manager.'
    },
    {
      name: 'Support',
      icon: '🛠️',
      color: 'bg-green-500 border-green-400',
      hoverText: 'I fix things before they break. It\'s like time travel, but with more coffee.'
    },
    {
      name: 'DevOps',
      icon: '🚀',
      color: 'bg-purple-500 border-purple-400',
      hoverText: 'Automation is my love language. If I\'m doing it twice, I\'m scripting it.'
    },
    {
      name: 'Open Source',
      icon: '⚡',
      color: 'bg-orange-500 border-orange-400',
      hoverText: 'Nothing beats the rush of getting a PR accepted at 3AM on a Tuesday.'
    }
  ];

  return (
    <div className={`${className}`}>
      <div className="flex flex-wrap justify-center gap-2 mb-6">
        {badges.map((badge, index) => (
          <motion.button
            key={badge.name}
            className={`flex items-center gap-1.5 px-3 py-1.5 rounded-md text-sm font-medium cursor-pointer border transition-all duration-300 ${activeIndex === index
              ? `border-secondary text-secondary shadow-lg`
              : 'bg-dark/50 text-tertiary border-tertiary/20 hover:border-secondary/50'}`}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => handleBadgeClick(index)}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <span className="text-lg">{badge.icon}</span>
            {badge.name}
          </motion.button>
        ))}
      </div>

      <AnimatePresence mode="wait">
        <motion.div
          key={activeIndex}
          className="relative bg-dark/30 border border-secondary/20 rounded-lg p-4 shadow-lg max-w-md mx-auto w-full"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.3 }}
        >
          <div className="absolute -top-2 left-1/2 -ml-2 w-4 h-4 bg-dark/30 border-t border-l border-secondary/20 transform rotate-45"></div>
          <p className="text-secondary italic text-center">"{badges[activeIndex].hoverText}"</p>
        </motion.div>
      </AnimatePresence>
    </div>
  );
};

export default TechBadges;
