import SectionTitle from '../components/SectionTitle';
import FadeInSection from '../components/FadeInSection';
import TechBadges from '../components/TechBadges';

const About = () => {
  return (
    <section id="about" className="py-20 bg-primary">
      <div className="section-container">
        <SectionTitle number="01" title="About Me" />
        
        <div className="grid md:grid-cols-3 gap-12 items-start">
          <FadeInSection className="md:col-span-2" delay={0.2}>
            <div className="space-y-4 text-tertiary">
              <TechBadges />
              
              <p>
                Hey there! I'm <PERSON>, a tech enthusiast who's turned my obsession with solving complex problems into a career. 
                I spend my days wrangling cloud systems (mostly AWS and GCP) and nights tinkering with whatever new tech has caught my eye this week.
              </p>
              
              <p>
                My tech journey kicked off at Penn State, where what started as a campus support gig quickly evolved into 
                building automation tools to make everyone's lives easier (including mine - I'm efficient like that). 
                Fast forward through a few roles where I kept finding ways to improve systems, and now I'm at AlgOPro Solutions 
                keeping high-frequency trading infrastructure running smoothly. Nothing like the pressure of knowing milliseconds 
                of downtime can cost serious money to keep you on your toes!
              </p>
              
              <p>
                I'm that guy who's always testing beta releases and contributing to open source projects. Call me an early adopter, 
                but there's something exciting about being on the bleeding edge and helping shape the tools we'll all be using tomorrow. 
                My GitHub commits often happen at 2AM when I should definitely be sleeping.
              </p>
              
              <p>
                When I actually log off (it happens occasionally), you'll find me hunting down hole-in-the-wall restaurants,
                planning my next trip, or at the gym trying to counteract all those hours at the keyboard. Balance, right?
              </p>
            </div>
          </FadeInSection>

          <FadeInSection delay={0.4} direction="left">
            <div className="relative group max-w-[250px] mx-auto">
              <div className="relative z-10 bg-primary border-2 border-secondary rounded-full overflow-hidden">
                <img 
                  src="/profile-image.jpg" 
                  alt="Michael Hoang" 
                  className="w-full h-auto grayscale hover:grayscale-0 transition-all duration-300"
                />
              </div>
              <div className="absolute -inset-4 border-2 border-secondary/50 rounded-full -z-10 group-hover:translate-x-2 group-hover:translate-y-2 transition-all duration-300"></div>
              
              <div className="mt-6 text-center text-secondary text-sm font-mono">
                <span className="typing-cursor">console.log('hire this guy');</span>
              </div>
            </div>
          </FadeInSection>
        </div>
      </div>
    </section>
  );
};

export default About;
