import { motion } from 'framer-motion';
import { Link } from 'react-scroll';
import { FiArrowDown } from 'react-icons/fi';
import styled from 'styled-components';
import { useEffect } from 'react';

const HeroSection = styled.section`
  min-height: 90vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  width: 100%;
  padding-top: 0;
`;

const BackgroundGradient = styled.div`
  position: absolute;
  inset: 0;
  background: linear-gradient(to bottom, rgba(10, 25, 47, 0.5), var(--primary));
  z-index: 0;
`;

const BackgroundPattern = styled.div`
  position: absolute;
  inset: 0;
  opacity: 0.05;
  z-index: 0;
`;

const ContentContainer = styled.div`
  position: relative;
  z-index: 10;
  max-width: 1000px;
  width: 100%;
  padding: 0 1.5rem;

  @media (min-width: 768px) {
    padding: 0 2rem;
  }
`;

const Greeting = styled(motion.div)`
  margin-bottom: 0.75rem;

  span {
    color: var(--secondary);
    font-family: 'Roboto Mono', monospace;
  }
`;

const Name = styled(motion.h1)`
  font-size: 3rem;
  font-weight: 700;
  color: var(--light);
  margin-bottom: 1rem;

  @media (min-width: 768px) {
    font-size: 4.5rem;
  }
`;

const Subtitle = styled(motion.h2)`
  font-size: 2.25rem;
  font-weight: 700;
  color: var(--tertiary);
  margin-bottom: 2rem;

  @media (min-width: 768px) {
    font-size: 3.75rem;
  }
`;

const Description = styled(motion.p)`
  font-size: 1.125rem;
  color: var(--tertiary);
  max-width: 42rem;
  margin-bottom: 3rem;
`;

const ButtonContainer = styled(motion.div)`
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
`;

const PrimaryButton = styled(Link)`
  padding: 0.75rem 1.5rem;
  background-color: transparent;
  color: var(--secondary);
  border: 1px solid var(--secondary);
  border-radius: 4px;
  font-size: 0.875rem;
  font-family: 'Roboto Mono', monospace;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
  text-align: center;

  &:hover {
    background-color: rgba(100, 255, 218, 0.1);
    transform: translateY(-2px);
  }
`;

const SecondaryButton = styled.a`
  padding: 0.75rem 1.5rem;
  background-color: var(--secondary);
  color: var(--primary);
  border: 1px solid var(--secondary);
  border-radius: 4px;
  font-size: 0.875rem;
  font-family: 'Roboto Mono', monospace;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
  text-align: center;

  &:hover {
    background-color: rgba(100, 255, 218, 0.9);
    transform: translateY(-2px);
  }
`;

const ScrollIndicator = styled(motion.div)`
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;

  span {
    color: var(--secondary);
    font-size: 0.75rem;
    margin-bottom: 0.5rem;
    font-family: 'Roboto Mono', monospace;
  }

  a {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--secondary);
    text-decoration: none;
  }

  svg {
    animation: bounce 2s infinite;
  }

  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-10px);
    }
    60% {
      transform: translateY(-5px);
    }
  }
`;

const Hero = () => {
  // Add debug logging
  console.log('Hero component rendering');
  
  useEffect(() => {
    console.log('Hero component mounted');
  }, []);
  
  return (
    <HeroSection id="hero">
      {/* Background gradient */}
      <BackgroundGradient />

      {/* Background grid pattern */}
      <BackgroundPattern className="bg-grid-pattern" />

      <ContentContainer>
        <Greeting
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <span>Hi, my name is</span>
        </Greeting>

        <Name
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          Michael Hoang.
        </Name>

        <Subtitle
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          I optimize cloud systems & automate solutions.
        </Subtitle>

        <Description
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          I'm a Senior Technical Support Engineer specializing in cloud infrastructure, security automation, and DevOps practices. Currently, I'm focused on building resilient systems that keep high-frequency trading platforms running flawlessly at AlgOPro Solutions.
        </Description>

        <ButtonContainer
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <PrimaryButton
            to="contact"
            smooth={true}
            duration={500}
            offset={-70}
          >
            Get In Touch
          </PrimaryButton>
          <SecondaryButton
            href="/resume.pdf"
            target="_blank"
            rel="noopener noreferrer"
          >
            Resume
          </SecondaryButton>
        </ButtonContainer>
      </ContentContainer>

      {/* Scroll down indicator */}
      <ScrollIndicator
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{
          duration: 0.5,
          delay: 1,
          repeat: Infinity,
          repeatType: "reverse",
          repeatDelay: 0.2
        }}
      >
        <Link
          to="about"
          smooth={true}
          duration={500}
          offset={-70}
        >
          <span>Scroll Down</span>
          <FiArrowDown />
        </Link>
      </ScrollIndicator>
    </HeroSection>
  );
};

export default Hero;
