import SectionTitle from '../components/SectionTitle';
import FadeInSection from '../components/FadeInSection';
import TechBadges from '../components/TechBadges';

const About = () => {
  return (
    <section id="about" className="py-20 bg-primary">
      <div className="section-container">
        <SectionTitle number="01" title="About Me" />

        <div className="flex flex-col items-center w-full">
          <FadeInSection delay={0.4}>
            <div className="flex flex-col items-center mb-12">
              {/* TechBadges moved above profile picture */}
              <TechBadges className="w-full max-w-md mx-auto mb-8" />

              {/* Profile picture in bubble style */}
              <div className="relative group max-w-[120px] mb-4 cursor-pointer">
                <div className="relative z-10 bg-primary border-3 border-secondary rounded-full overflow-hidden shadow-lg">
                  <img
                    src="/profile-image.jpg"
                    alt="Michael Hoang"
                    className="w-full h-auto grayscale hover:grayscale-0 transition-all duration-300"
                  />
                  <div className="absolute inset-0 bg-primary/80 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                    <p className="text-secondary text-xs font-medium">Ain't He A Beauty</p>
                  </div>
                </div>
                <div className="absolute -inset-2 border-2 border-secondary/30 rounded-full -z-10 group-hover:translate-x-1 group-hover:translate-y-1 transition-all duration-300"></div>
              </div>

              <div className="mt-4 text-center text-secondary text-sm font-mono">
                <span className="typing-cursor">console.log('you should hire me');</span>
              </div>
            </div>
          </FadeInSection>

          <FadeInSection delay={0.2}>
            <div className="space-y-4 text-tertiary max-w-2xl mx-auto">
              <p>
                My tech journey started with dismantling computers as a kid, coding mods and scripting bots for games. This early fascination evolved into a career where AWS and GCP became my playgrounds.
              </p>

              <p>
                I'm an early technology adopter at heart, always figuring out how to leverage the newest AI and integrating MCP into security and cloud tools. I contribute to open source, experiment with emerging frameworks, and implement technologies before they become mainstream.
              </p>

              <p>
                At Penn State, I transformed a support role by creating automation tools adopted campus-wide. At AlgOPro Solutions, I built mission-critical infrastructure where milliseconds matter. My approach to leadership focuses on solving real problems, elevating teams, and mentoring junior engineers.
              </p>

              <p>
                Off the clock, you'll find me enjoying good laughs, great food, and quality family time.
              </p>
            </div>
          </FadeInSection>
        </div>
      </div>
    </section>
  );
};

export default About;
