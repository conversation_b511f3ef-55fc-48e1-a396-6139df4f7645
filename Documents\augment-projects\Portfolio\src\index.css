@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Roboto+Mono:wght@100;200;300;400;500;600;700&display=swap');

:root {
  --primary: #0a192f;
  --secondary: #64ffda;
  --tertiary: #8892b0;
  --light: #ccd6f6;
  --dark: #020c1b;
}

.light-theme {
  --primary: #f5f5f5;
  --secondary: #0a9396;
  --tertiary: #4a5568;
  --light: #1a202c;
  --dark: #e2e8f0;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  font-family: 'Inter', sans-serif;
  background-color: var(--primary);
  color: var(--light);
  overflow-x: hidden;
}

/* Global styles */
.section-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 4rem 1rem;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.section-container > * {
  width: 100%;
  max-width: 900px;
}

@media (min-width: 768px) {
  .section-container {
    padding: 6rem 1.5rem;
  }
}

.btn-primary {
  padding: 0.75rem 1.5rem;
  background-color: transparent;
  border: 2px solid var(--secondary);
  color: var(--secondary);
  font-weight: 500;
  border-radius: 0.25rem;
  display: inline-block;
  transition: all 0.3s ease;
  cursor: pointer;
  text-decoration: none;
}

.btn-primary:hover {
  background-color: rgba(100, 255, 218, 0.1);
}

/* Grid pattern background */
.bg-grid-pattern {
  background-size: 50px 50px;
  background-image:
    linear-gradient(to right, rgba(100, 255, 218, 0.03) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(100, 255, 218, 0.03) 1px, transparent 1px);
}

/* Typing cursor animation */
.typing-cursor {
  position: relative;
}

.typing-cursor::after {
  content: '|';
  position: absolute;
  right: -4px;
  color: var(--secondary);
  animation: cursor-blink 1s step-end infinite;
}

@keyframes cursor-blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}
