import { useEffect } from 'react';
import { ThemeProvider } from 'styled-components';

// Styles
import GlobalStyles from './styles/GlobalStyles';
import theme from './styles/theme';

function SimpleApp() {
  // Add debug logging
  console.log('SimpleApp component initializing');
  
  // Add a class to the body when the component mounts
  useEffect(() => {
    console.log('SimpleApp component mounted');
    document.body.classList.add('bg-grid-pattern');

    return () => {
      document.body.classList.remove('bg-grid-pattern');
    };
  }, []);

  return (
    <ThemeProvider theme={theme}>
      <GlobalStyles />
      <div style={{ 
        position: 'relative', 
        display: 'flex', 
        flexDirection: 'column', 
        alignItems: 'center', 
        width: '100%',
        minHeight: '100vh',
        backgroundColor: '#0a192f',
        color: '#ccd6f6',
        padding: '20px'
      }}>
        <h1>Simple Portfolio App</h1>
        <p>If you can see this, the ThemeProvider and GlobalStyles are working correctly.</p>
      </div>
    </ThemeProvider>
  );
}

export default SimpleApp;
