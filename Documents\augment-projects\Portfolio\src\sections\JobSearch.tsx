
import styled from 'styled-components';
import { motion } from 'framer-motion';
import SectionTitle from '../components/SectionTitle';
import JobSearchApp from '../components/JobSearch/JobSearchApp';

const JobSearch = () => {
  return (
    <JobSearchSection id="jobsearch">
      <div className="section-container">
        <SectionTitle number="07" title="Job Search" />

        <SectionDescription
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.5 }}
        >
          Search across multiple job boards in one place, removing duplicates and saving your favorites.
        </SectionDescription>

        <JobSearchAppContainer>
          <JobSearchApp />
        </JobSearchAppContainer>
      </div>
    </JobSearchSection>
  );
};

const JobSearchSection = styled.section`
  padding: 6rem 0;
  width: 100%;
`;

const SectionDescription = styled(motion.p)`
  color: var(--tertiary);
  font-size: 1.1rem;
  max-width: 600px;
  text-align: center;
  margin: 0 auto 3rem;
  line-height: 1.6;
`;

const JobSearchAppContainer = styled.div`
  width: 100%;
`;

export default JobSearch;
