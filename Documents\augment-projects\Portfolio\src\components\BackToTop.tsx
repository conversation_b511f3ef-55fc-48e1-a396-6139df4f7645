import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiArrowUp } from 'react-icons/fi';
import { Link } from 'react-scroll';

const BackToTop = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > 500) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener('scroll', toggleVisibility);
    return () => window.removeEventListener('scroll', toggleVisibility);
  }, []);

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className="fixed bottom-6 right-6 z-50"
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.5 }}
          transition={{ duration: 0.3 }}
        >
          <Link
            to="hero"
            smooth={true}
            duration={500}
            className="flex items-center justify-center w-10 h-10 bg-secondary text-primary rounded-full shadow-lg cursor-pointer hover:bg-secondary/90 transition-colors"
            aria-label="Back to top"
          >
            <FiArrowUp size={20} />
          </Link>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default BackToTop;
