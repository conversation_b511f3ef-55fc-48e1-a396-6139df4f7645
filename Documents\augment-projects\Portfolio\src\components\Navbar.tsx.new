import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-scroll';
import { FiMenu, FiX } from 'react-icons/fi';
import styled from 'styled-components';

const NavContainer = styled.nav<{ scrolled: boolean }>`
  position: fixed;
  width: 100%;
  z-index: 50;
  transition: all 0.3s ease;
  background-color: ${({ scrolled }) => scrolled ? 'rgba(10, 25, 47, 0.9)' : 'transparent'};
  backdrop-filter: ${({ scrolled }) => scrolled ? 'blur(10px)' : 'none'};
  padding: ${({ scrolled }) => scrolled ? '0.75rem 0' : '1.25rem 0'};
  box-shadow: ${({ scrolled }) => scrolled ? '0 4px 20px rgba(0, 0, 0, 0.1)' : 'none'};
  display: flex;
  justify-content: center;
  margin-top: 0.5rem;
  top: 0;
`;

const NavInner = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 4rem;
  width: 100%;

  @media (min-width: 768px) {
    padding: 0 1.5rem;
  }

  @media (min-width: 1024px) {
    padding: 0 2rem;
  }
`;

const Logo = styled.span`
  color: var(--secondary);
  font-family: 'Roboto Mono', monospace;
  font-size: 1.5rem;
  font-weight: 700;
  position: absolute;
  left: 1rem;
  top: 1.75rem;
`;

const DesktopMenu = styled.div`
  display: none;

  @media (min-width: 768px) {
    display: flex;
    justify-content: center;
    width: 100%;
  }
`;

const NavLinks = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  width: 100%;
`;

const NavLink = styled(Link)`
  color: var(--tertiary);
  font-size: 0.875rem;
  font-weight: 500;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover, &.active {
    color: var(--secondary);
  }

  &::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--secondary);
    transition: width 0.2s ease;
  }

  &:hover::after, &.active::after {
    width: 100%;
  }

  @media (min-width: 1024px) {
    font-size: 1rem;
  }
`;

const ResumeButton = styled.a`
  color: var(--secondary);
  background-color: transparent;
  border: 1px solid var(--secondary);
  border-radius: 0.25rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;

  &:hover {
    background-color: rgba(100, 255, 218, 0.1);
    transform: translateY(-2px);
  }

  @media (min-width: 1024px) {
    font-size: 1rem;
    padding: 0.625rem 1.25rem;
  }
`;

const MobileMenuButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  color: var(--secondary);
  border-radius: 0.375rem;
  position: absolute;
  right: 1rem;

  &:hover {
    color: white;
    background-color: var(--primary);
  }

  @media (min-width: 768px) {
    display: none;
  }
`;

const MobileMenu = styled(motion.div)`
  @media (min-width: 768px) {
    display: none;
  }
`;

const MobileMenuContent = styled.div`
  padding: 0.5rem;
  background-color: rgba(10, 25, 47, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
`;

const MobileNavLink = styled(Link)`
  display: block;
  padding: 0.75rem 1rem;
  color: var(--tertiary);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;

  &:hover, &.active {
    color: var(--secondary);
    background-color: rgba(100, 255, 218, 0.05);
  }
`;

const MobileResumeLink = styled.a`
  display: block;
  margin: 0.75rem 1rem;
  padding: 0.75rem;
  color: var(--secondary);
  background-color: transparent;
  border: 1px solid var(--secondary);
  border-radius: 0.25rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  text-align: center;

  &:hover {
    background-color: rgba(100, 255, 218, 0.1);
  }
`;

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const offset = window.scrollY;
      if (offset > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const navLinks = [
    { name: 'Home', to: 'hero', offset: 0 },
    { name: 'About', to: 'about', offset: -70 },
    { name: 'Skills', to: 'skills', offset: -70 },
    { name: 'Experience', to: 'experience', offset: -70 },
    { name: 'Projects', to: 'projects', offset: -70 },
    { name: 'Certifications', to: 'certifications', offset: -70 },
    { name: 'Job Search', to: 'jobsearch', offset: -70 },
    { name: 'Contact', to: 'contact', offset: -70 },
  ];

  return (
    <NavContainer scrolled={scrolled}>
      <NavInner>
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Link to="hero" smooth={true} duration={500}>
            <Logo>MDH</Logo>
          </Link>
        </motion.div>

        {/* Desktop menu */}
        <DesktopMenu>
          <NavLinks>
            {navLinks.map((link, index) => (
              <motion.div
                key={link.name}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <NavLink
                  activeClass="active"
                  to={link.to}
                  spy={true}
                  smooth={true}
                  offset={link.offset}
                  duration={500}
                >
                  {link.name}
                </NavLink>
              </motion.div>
            ))}
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: navLinks.length * 0.1 }}
            >
              <ResumeButton
                href="/resume.pdf"
                target="_blank"
                rel="noopener noreferrer"
              >
                Resume
              </ResumeButton>
            </motion.div>
          </NavLinks>
        </DesktopMenu>

        {/* Mobile menu button */}
        <MobileMenuButton onClick={() => setIsOpen(!isOpen)}>
          {isOpen ? <FiX size={24} /> : <FiMenu size={24} />}
        </MobileMenuButton>
      </NavInner>

      {/* Mobile menu */}
      <MobileMenu
        initial={{ opacity: 0, height: 0 }}
        animate={{ opacity: isOpen ? 1 : 0, height: isOpen ? 'auto' : 0 }}
        transition={{ duration: 0.3 }}
        style={{ display: isOpen ? 'block' : 'none' }}
      >
        <MobileMenuContent>
          {navLinks.map((link) => (
            <MobileNavLink
              key={link.name}
              activeClass="active"
              to={link.to}
              spy={true}
              smooth={true}
              offset={link.offset}
              duration={500}
              onClick={() => setIsOpen(false)}
            >
              {link.name}
            </MobileNavLink>
          ))}
          <MobileResumeLink
            href="/resume.pdf"
            target="_blank"
            rel="noopener noreferrer"
          >
            Resume
          </MobileResumeLink>
        </MobileMenuContent>
      </MobileMenu>
    </NavContainer>
  );
};

export default Navbar;
