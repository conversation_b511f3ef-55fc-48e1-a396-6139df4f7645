# Environment variables for Predictive Threat Intelligence Platform
# This is an example file. Copy this to .env.local and fill in your actual values.
# .env.local should NOT be committed to Git.

# --- OSINT Collector ---
# Replace with your actual AlienVault OTX API Key
OTX_API_KEY="YOUR_OTX_API_KEY_HERE"

# Replace with the GCS bucket name for storing RAW threat data
GCS_RAW_BUCKET="your-gcs-raw-data-bucket-name"

# VirusTotal API Key (Get from your VT account)
VT_API_KEY="YOUR_VT_API_KEY_HERE"

# --- Add other environment variables as needed ---
# e.g., GCS_NORMALIZED_BUCKET="your-gcs-normalized-data-bucket-name"
# e.g., BIGQUERY_PROJECT_ID="your-gcp-project-id"
# e.g., BIGQUERY_DATASET_ID="threat_intelligence"
# e.g., BIGQUERY_TABLE_ID="normalized_threats"
