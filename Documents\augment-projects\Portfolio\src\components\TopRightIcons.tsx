import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, FiMoon } from 'react-icons/fi';
import { useState, useEffect } from 'react';
import styled from 'styled-components';

const IconContainer = styled.div`
  position: fixed;
  top: 0.25rem;
  right: 1rem;
  display: flex;
  gap: 0.5rem;
  z-index: 100;
`;

const IconButton = styled(motion.button)`
  width: 2rem;
  height: 2rem;
  border-radius: 9999px;
  background-color: rgba(100, 255, 218, 0.1);
  border: 1px solid rgba(100, 255, 218, 0.3);
  color: var(--secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
  
  &:hover {
    background-color: rgba(100, 255, 218, 0.2);
  }
`;

const TopRightIcons = () => {
  const [isDark, setIsDark] = useState(true);

  useEffect(() => {
    // Check if user has a theme preference in localStorage
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
      setIsDark(savedTheme === 'dark');
    } else {
      // Check if user prefers dark mode
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      setIsDark(prefersDark);
    }
  }, []);

  useEffect(() => {
    // Apply theme to document
    document.documentElement.classList.toggle('light-theme', !isDark);

    // Save preference to localStorage
    localStorage.setItem('theme', isDark ? 'dark' : 'light');
  }, [isDark]);

  const handlePrintClick = () => {
    window.open('/resume.pdf', '_blank');
  };

  return (
    <IconContainer>
      <IconButton
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        onClick={() => setIsDark(!isDark)}
        aria-label={isDark ? 'Switch to light mode' : 'Switch to dark mode'}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3, delay: 0.5 }}
      >
        {isDark ? <FiSun size={16} /> : <FiMoon size={16} />}
      </IconButton>
      
      <IconButton
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        onClick={handlePrintClick}
        aria-label="Print resume"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3, delay: 0.6 }}
      >
        <FiPrinter size={16} />
      </IconButton>
    </IconContainer>
  );
};

export default TopRightIcons;
