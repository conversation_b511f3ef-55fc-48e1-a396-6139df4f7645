const fs = require('fs-extra');
const path = require('path');

const buildDir = path.join(__dirname, 'dist');
const sourceDir = __dirname;

// clear the build directory
fs.emptyDirSync(buildDir);

// copy all files and directories from the source to the build directory, except for node_modules, build.js, and dist
fs.copySync(sourceDir, buildDir, {
  filter: (src, dest) => {
    const srcPath = path.resolve(src);
    const nodeModulesPath = path.join(sourceDir, 'node_modules');
    const buildJsPath = path.join(sourceDir, 'build.js');
    const distPath = path.join(sourceDir, 'dist');

    return (
      srcPath !== nodeModulesPath &&
      srcPath !== buildJsPath &&
      srcPath !== distPath
    );
  },
});
